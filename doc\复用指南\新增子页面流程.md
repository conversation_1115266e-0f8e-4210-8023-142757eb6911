# Next.js 子页面创建流程

## 1. 创建页面组件
在 `app/[locale]/(default)` 下创建新页面目录和组件:

```plaintext
app/
  └── [locale]/
      └── (default)/
          └── newpage/              # 新页面目录
              └── page.tsx          # 页面组件
```

## 2. 创建翻译文件
在 `i18n/pages` 下创建对应翻译文件:

```plaintext
i18n/
  └── pages/
      └── newpage/                 # 新页面翻译目录
          ├── en.json              # 英文翻译
          └── zh.json              # 中文翻译（可选）
```

## 3. 添加服务函数
在 `services/page.ts` 中添加获取页面数据的函数:

```typescript
export async function getNewPage(locale: string) {
  try {
    if (locale === "zh-CN") {
      locale = "zh";
    }
    return await import(
      `@/i18n/pages/newpage/${locale.toLowerCase()}.json`
    ).then((module) => module.default);
  } catch (error) {
    console.warn(`Failed to load newpage/${locale}.json, falling back to en.json`);
    return await import("@/i18n/pages/newpage/en.json").then(
      (module) => module.default
    );
  }
}
```

## 4. 编写页面组件
创建基本页面结构:

```typescript
import { getNewPage } from "@/services/page";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/newpage`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/newpage`;
  }

  return {
    title: "New Page - our-service",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function NewPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getNewPage(locale);

  return (
    <main className="flex min-h-screen flex-col">
      {page.section1 && <Component1 section={page.section1} />}
      {page.section2 && <Component2 section={page.section2} />}
    </main>
  );
}
```

## 5. 可复用组件
从 `components/blocks` 中选择合适的组件:
- `Hero`: 页面头部展示
- `Feature1/Feature2/Feature3`: 特性展示
- `CTA`: 号召性按钮
- `Branding`: 品牌展示
- `Stats`: 数据统计
- `FAQ`: 常见问题
- `Testimonial`: 用户评价

## 6. 测试和调试
1. 确保翻译文件格式正确
2. 使用 `console.log` 检查数据流
3. 访问以下路径测试:
   - `http://localhost:3000/newpage` (英文)
   - `http://localhost:3000/zh/newpage` (中文)

## 注意事项
- 保持与现有页面结构一致
- 复用现有组件优先
- 保持多语言支持
- 确保响应式设计