import "@/app/globals.css";
import React from "react";
import { Metadata } from "next";
import { getTranslations, getMessages } from "next-intl/server";
import { NextIntlClientProvider } from "next-intl";
import { NextAuthSessionProvider } from "@/auth/session";
import { AppContextProvider } from "@/contexts/app";
import Header from "@/components/blocks/header";
import Footer from "@/components/blocks/footer";
import { getLandingPage } from "@/services/page";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations();
  return {
    title: {
      template: `%s | ${t("metadata.title")}`,
      default: t("metadata.title"),
    },
    description: t("metadata.description"),
    keywords: t("metadata.keywords"),
  };
}

export default async function LegalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // 国际化消息（默认为 en）
  const messages = await getMessages();
  // 取 header/footer 数据
  const page = await getLandingPage("en");

  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        {/* 保证 useTranslations、useSession、useAppContext 都有上下文 */}
        <NextIntlClientProvider messages={messages}>
          <NextAuthSessionProvider>
            <AppContextProvider>
              {/* 渲染统一头部 */}
              {page.header && <Header header={page.header} />}

              {/* 法律页正文 */}
              <div
                className="
                  text-md max-w-3xl mx-auto leading-loose 
                  pt-4 pb-8 px-8 
                  prose prose-slate dark:prose-invert 
                  prose-headings:font-semibold 
                  prose-a:text-primary hover:prose-a:text-primary/80 
                  prose-strong:text-base-content 
                  prose-code:text-base-content prose-code:bg-muted 
                  prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded-md
                "
              >
                {children}
              </div>

              {/* 渲染统一底部 */}
              {page.footer && <Footer footer={page.footer} />}
            </AppContextProvider>
          </NextAuthSessionProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
