//创建Creem.io Webhook处理路由
import { NextRequest, NextResponse } from 'next/server';
import { CreemService } from '@/services/creem';
import { CreemWebhookEvent } from '@/types/creem';
import { handleCreemOrderSession } from '@/services/order';
import { respOk } from '@/lib/resp';

export async function POST(req: NextRequest) {
  try {
    // 获取 Creem webhook 密钥
    const creemWebhookSecret = process.env.CREEM_WEBHOOK_SECRET;

    if (!creemWebhookSecret) {
      console.error("CREEM_WEBHOOK_SECRET not configured");
      return NextResponse.json(
        { error: "Webhook secret not configured" },
        { status: 500 }
      );
    }

    // 获取请求体和签名
    const body = await req.text();
    const signature = req.headers.get("creem-signature");

    if (!signature || !body) {
      console.error("Invalid webhook data: missing signature or body");
      return NextResponse.json(
        { error: "Invalid webhook data" },
        { status: 400 }
      );
    }

    // 验证 webhook 签名
    const creemService = CreemService.getInstance();
    const isValidSignature = creemService.verifyWebhookSignature(body, signature);

    if (!isValidSignature) {
      console.error("Invalid webhook signature");
      return NextResponse.json(
        { error: "Invalid signature" },
        { status: 401 }
      );
    }

    // 解析 webhook 事件
    const event: CreemWebhookEvent = JSON.parse(body);
    console.log("Creem webhook event received:", event.eventType, event.id);

    // 处理不同类型的事件
    switch (event.eventType) {
      case "checkout.completed": {
        console.log("Processing checkout.completed event");
        await handleCreemOrderSession(event.object);
        break;
      }

      case "subscription.paid": {
        console.log("Processing subscription.paid event");
        await handleCreemOrderSession(event.object);
        break;
      }

      case "subscription.active": {
        console.log("Processing subscription.active event");
        // 订阅激活事件，可以用于同步状态
        console.log("Subscription activated:", event.object.id);
        break;
      }

      case "subscription.canceled": {
        console.log("Processing subscription.canceled event");
        // 订阅取消事件，可以用于更新用户权限
        console.log("Subscription canceled:", event.object.id);
        break;
      }

      case "subscription.expired": {
        console.log("Processing subscription.expired event");
        // 订阅过期事件
        console.log("Subscription expired:", event.object.id);
        break;
      }

      case "refund.created": {
        console.log("Processing refund.created event");
        // 退款事件
        console.log("Refund created for:", event.object.transaction?.id);
        break;
      }

      case "subscription.update": {
        console.log("Processing subscription.update event");
        // 订阅更新事件
        console.log("Subscription updated:", event.object.id);
        break;
      }

      case "subscription.trialing": {
        console.log("Processing subscription.trialing event");
        // 试用期开始事件
        console.log("Subscription trial started:", event.object.id);
        break;
      }

      default:
        console.log("Unhandled event type:", event.eventType);
    }

    return respOk();

  } catch (e: any) {
    console.error("Creem webhook processing failed:", e);
    return NextResponse.json(
      { error: `Handle Creem webhook failed: ${e.message}` },
      { status: 500 }
    );
  }
}
