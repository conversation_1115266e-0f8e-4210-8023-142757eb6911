//使用 Replicate 上的图片模型生成图片

import { respData, respErr } from "@/lib/resp";
import { experimental_generateImage as generateImage } from "ai";
import { replicate } from "@ai-sdk/replicate";
import path from "path";
import { writeFile } from "fs/promises";
import { v4 as getUuid } from 'uuid';
import { Image } from "@/types/image";
import { insertImage } from "@/models/image";
import { getUserUuid } from "@/services/user";
import { CreditsAmount, CreditsTransType, getUserCredits, decreaseCredits } from "@/services/credit";
//import { createClient } from "@supabase/supabase-js";

export async function POST(req: Request) {
    try{
      const{description} = await req.json();
      if (!description || typeof description !== "string") {
        console.error("Invalid description received:", description);
        return respErr("Invalid description");
      }

      console.log("Generating image with description:", description);

      const user_uuid=await getUserUuid();
      if(!user_uuid){
        return respErr("User not logged in");
      }
      //const cost_credits=1;
      const credits=await getUserCredits(user_uuid);
      if(credits.left_credits<CreditsAmount.GenImageCost){
        return respErr("Not enough credits");
      }

      const model = "black-forest-labs/flux-schnell";
      const prompt=`A high-resolution Studio Ghibli-style illustration of ${description}, featuring soft pastel colors, intricate hand-drawn details, and a whimsical, cozy atmosphere. The scene includes lush natural elements like vibrant forests, sparkling streams, or blooming meadows, with gentle lighting and a dreamy, emotional tone. Emphasize delicate textures, warm highlights, and a painterly aesthetic inspired by Hayao Miyazaki's films. No text, ultra-detailed, 4K resolution`;
      
      const imageModel = replicate.image(model);
      const providerOptions = {
        replicate: {
          output_quality: 90,
        },
       };
 
       const { images } = await generateImage({
         model: imageModel,
         prompt: prompt,
         n: 1,
         providerOptions,
        });

        console.log("Replicate API response - images:", images);

        if (!images || images.length === 0) {
            console.error("No images generated by Replicate");
            return respErr("No images generated");
        }
           
          const batch = getUuid();

          const provider = "replicate";
           
          const genImages = await Promise.all(
            images.map(async (image, index) => {
              const fileName = `${provider}_image_${batch}_${index}.png`;
              const filePath = path.join(process.cwd(), "public", fileName);
              const url = `${process.env.NEXT_PUBLIC_WEB_URL}/${fileName}`;
              const buffer = Buffer.from(image.base64, "base64");

              console.log("Writing image to:", filePath);
              await writeFile(filePath, buffer);
              console.log("Image written successfully:", fileName);
           
              return {
                uuid : getUuid(),
                img_description: prompt,
                img_url: url,
                status: "created",
                //created_at: new Date().toISOString(),
              }as Image;
            })
          );
      
      console.log("Inserting images to database:", genImages);
      await insertImage(genImages).catch((dbError) => {
        console.error("Database insertion failed:", dbError);
        throw dbError;
      });
      console.log("Database insertion successful");

      try {
        await decreaseCredits({
          user_uuid,
          trans_type: CreditsTransType.GenImage,
          credits: CreditsAmount.GenImageCost,
        });
      } catch (creditError) {
        console.error("Failed to decrease credits:", creditError);
        return respErr("Image generated, but failed to decrease credits: " + (creditError as Error).message);
      }

      const updatedCredits = await getUserCredits(user_uuid);

      return respData({
        prompt:prompt,
        images:genImages,
        left_credits:updatedCredits.left_credits,
      });
    }catch(e){
        console.error("Generate image error:", e);
        return respErr("Failed to generate image: " + (e as Error).message);
    }
}