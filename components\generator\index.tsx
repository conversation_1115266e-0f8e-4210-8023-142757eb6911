"use client";
import { toast } from "sonner";
import { useState } from "react";
import { Button } from "@/components/ui/button";

export default function GeneratorInput({
  onGenerate,
}: {
  onGenerate?: (desc: string, imgUrl?: string) => void;
}) {
  const [desc, setDesc] = useState("");
  const [loading, setLoading] = useState(false);

  const requestGenImage = async () => {
    try {
      setLoading(true);
      const resp = await fetch("/api/gen-image", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ description: desc }),
      });
      const data = await resp.json();
      if (data.code === -1) {
        toast.error(data.message || "Failed to generate image. Please try again.");
        return null;
      }
      toast.success("Image generated successfully!");
      return data;
    } catch (error) {
      toast.error("Failed to generate image. Please try again.");
      console.error("Image generation error:", error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const handleGenerate = async () => {
    if (!desc.trim()) {
      toast.error("Please enter a valid description");
      return;
    }
    const result = await requestGenImage();
    if (result && result.images && result.images[0]?.img_url) {
      const event = new CustomEvent("generate", {
        detail: { desc, imgUrl: result.images[0].img_url },
      });
      window.dispatchEvent(event);
      if (onGenerate) {
        onGenerate(desc, result.images[0].img_url);
      }
    }
  };

  return (
    <div className="flex items-center w-full max-w-xl mx-auto mb-16">
      <input
        type="text"
        className="flex-1 rounded-md border border-gray-300 px-4 py-2 text-base outline-none focus:border-primary"
        placeholder="Describe a Ghibli-style scene, e.g., a forest cottage at twilight"
        value={desc}
        onChange={(e) => setDesc(e.target.value)}
        disabled={loading}
      />
      <Button
        className="ml-2 bg-[#8f3cff] hover:bg-[#7a2be6] text-white px-6"
        onClick={handleGenerate}
        disabled={loading}
      >
        {loading ? "Generating..." : "Generate"}
      </Button>
    </div>
  );
}