"use client";
import React from "react";

export interface GhibliImage {
  id: string;
  title: string;
  img_url: string;
  dimensions?: string;
  author?: {
    name: string;
    avatarUrl?: string;
  };
}

export default function GhibliImageGallery({ images }: { images: GhibliImage[] }) {
  if (!Array.isArray(images) || images.length === 0) {
    return <div>No images available</div>; // 添加占位 UI
  }

  return (
    <div className="max-w-4xl mx-auto grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 ghibli-image-gallery">
      {images.map((img) => (
        <div
          key={img.id}
          className="bg-white rounded-xl shadow hover:shadow-lg transition overflow-hidden flex flex-col"
        >
          <img
            src={img.img_url}
            alt={img.title}
            className="w-full h-48 object-cover"
            loading="lazy"
          />
          <div className="flex-1 flex flex-col justify-between p-4">
            <div>
              <div className="text-base font-medium text-gray-900 truncate">
                {img.title}
              </div>
              <div className="text-sm text-gray-600">{img.dimensions || "1792×1024"}</div>
            </div>
            <div className="flex items-center justify-between mt-4">
              {img.author && img.author.avatarUrl ? (
                <img
                  src={img.author.avatarUrl}
                  alt={img.author.name}
                  className="w-8 h-8 rounded-full"
                />
              ) : (
                <UserAvatar name={img.author?.name} />
              )}
              <span className="text-gray-700 text-xs">{img.author?.name || "Unknown"}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// 用户头像组件，首字母或默认头像
function UserAvatar({ name }: { name?: string }) {
  if (!name) {
    return (
      <span className="w-8 h-8 flex items-center justify-center rounded-full bg-violet-500 text-white text-lg">
        <svg width="20" height="20" fill="currentColor">
          <path d="M10 10a4 4 0 100-8 4 4 0 000 8zm0 2c-3.31 0-6 1.34-6 3v1a1 1 0 001 1h10a1 1 0 001-1v-1c0-1.66-2.69-3-6-3z" />
        </svg>
      </span>
    );
  }
  return (
    <span className="w-8 h-8 flex items-center justify-center rounded-full bg-green-500 text-white text-lg uppercase">
      {name[0]}
    </span>
  );
}
