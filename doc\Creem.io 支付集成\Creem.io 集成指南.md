# Creem.io 支付系统集成指南

## 📋 概述

本文档描述了如何将 项目的支付系统从 Stripe 替换为 Creem.io。集成遵循最小化代码修改的原则，保持现有架构的一致性。

**适用场景**: SaaS 产品、订阅服务、一次性付费、积分充值等多种支付模式。

## 🔧 环境配置

### 1. 环境变量设置

在 `.env.local` 文件中添加以下 Creem.io 相关配置：

```bash
# Creem.io 支付配置
CREEM_API_KEY=your_creem_api_key_here
CREEM_API_URL=https://api.creem.io
CREEM_MODE=test  # 测试环境用 test，生产环境用 live
CREEM_WEBHOOK_SECRET=your_creem_webhook_secret_here

# 产品配置 - 需要在 Creem.io 后台创建对应产品后获取
FREE_PLAN_PRODUCT_ID=prod_free_plan_id_here
BASIC_PLAN_PRODUCT_ID=prod_basic_plan_id_here  
PRO_PLAN_PRODUCT_ID=prod_pro_plan_id_here
ELITE_PLAN_PRODUCT_ID=prod_elite_plan_id_here

# 支付页面配置
NEXT_PUBLIC_PAY_SUCCESS_URL=/creem-success
NEXT_PUBLIC_PAY_FAIL_URL=/creem-fail
NEXT_PUBLIC_PAY_CANCEL_URL=/
NEXT_PUBLIC_WEB_URL=http://localhost:3000  # 本地开发用，生产环境改为实际域名
```

### 2. Creem.io 账户配置

#### 账户注册和基础设置
1. 访问 [Creem.io](https://creem.io) 注册账户
2. 登录 [Creem.io Dashboard](https://creem.io/dashboard)
3. 确保当前在 **测试模式** (Test Mode) 下进行开发

#### API 密钥获取
1. 在 Dashboard 中找到 **API Keys** 页面
2. 复制 **Test Secret Key** (格式: `sk_test_xxxxxxxxxx`)
3. 将密钥添加到 `CREEM_API_KEY` 环境变量

#### Webhook 配置
1. 在 Dashboard 中找到 **Webhooks** 页面
2. 点击 **"Add Webhook Endpoint"**
3. 配置如下：
   ```
   URL: http://localhost:3000/api/creem-notify  (开发环境)
   URL: https://yourdomain.com/api/creem-notify  (生产环境)
   
   Events to send:
   ✅ checkout.completed
   ✅ checkout.expired  
   ✅ subscription.paid
   ✅ subscription.cancelled
   ✅ payment.failed
   ```
4. 复制 **Webhook Secret** 到 `CREEM_WEBHOOK_SECRET` 环境变量

#### 产品创建
参考 [Creem产品配置指南.md](./Creem产品配置指南.md) 创建对应的产品：

1. **免费版产品**：
   ```
   名称: Free PDF to Quiz Plan
   价格: $0.00
   类型: One-time
   描述: Perfect for trying our service. Limited conversions.
   ```

2. **基础版产品**：
   ```
   名称: Basic Quiz Generator Plan  
   价格: $12.00
   类型: Recurring (Monthly)
   描述: For light users. 5M characters per month.
   ```

3. **专业版产品**：
   ```
   名称: Pro Quiz Generator Plan
   价格: $25.00  
   类型: Recurring (Monthly)
   描述: For power users. 15M characters per month with advanced features.
   ```

4. **企业版产品**：
   ```
   名称: Elite Quiz Generator Plan
   价格: $45.00
   类型: Recurring (Monthly) 
   描述: For organizations. 30M characters per month with API access.
   ```

## 🏗️ 架构设计

### 设计原则

1. **简化优先**: 项目是工具型产品，重点在于积分充值和功能使用，避免复杂的订阅管理
2. **用户体验**: 通过 `my-orders` 和 `my-credits` 页面管理购买历史，无需额外账单界面
3. **成本控制**: 基于字符数和页数的双重限制，精确控制 AI 成本
4. **渐进增强**: 支持免费试用，逐步引导用户升级付费计划

### 核心组件

1. **类型定义** (`types/creem.d.ts`)
   - 完整的 Creem.io API TypeScript 类型定义
   - 支付会话、订单、客户、订阅等数据模型

2. **服务层** (`services/creem.ts`)
   - `CreemService` 类封装所有 Creem.io API 调用
   - 支持创建支付会话、获取订单信息、验证 Webhook 等

3. **API 路由**
   - `/api/creem-checkout` - 创建支付会话并跳转
   - `/api/creem-notify` - 处理 Webhook 通知和订单更新

4. **页面组件**
   - `/creem-success` - 支付成功页面，显示订单信息和积分
   - 更新的定价组件，支持多种计费模式

## 🔄 支付流程

### 1. 用户发起支付

```typescript
// 前端调用 (components/blocks/pricing/index.tsx)
const handleCheckout = async (plan: any) => {
  try {
const response = await fetch("/api/creem-checkout", {
  method: "POST",
      headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
        product_id: plan.product_id,
        user_email: session?.user?.email,
        amount: plan.amount,
        currency: plan.currency || "USD",
        interval: plan.interval,
        success_url: `${window.location.origin}/creem-success`,
        cancel_url: window.location.href,
  }),
});

    const data = await response.json();
    if (data.checkout_url) {
      window.location.href = data.checkout_url;
    }
  } catch (error) {
    console.error("Payment initiation failed:", error);
  }
};
```

### 2. 支付处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API(/api/creem-checkout)
    participant C as Creem.io
    participant W as Webhook(/api/creem-notify)
    participant D as 数据库

    U->>F: 点击购买按钮
    F->>A: POST 支付请求
    A->>C: 创建支付会话
    C->>A: 返回支付链接
    A->>F: 返回 checkout_url
    F->>C: 重定向到支付页面
    U->>C: 完成支付
    C->>W: 发送 Webhook 通知
    W->>D: 更新订单状态和用户积分
    C->>U: 重定向到成功页面
```

### 3. Webhook 事件处理

```typescript
// app/api/creem-notify/route.ts
export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const signature = req.headers.get("creem-signature");
    
    // 验证 Webhook 签名
    const isValid = creem.verifyWebhookSignature(body, signature);
    if (!isValid) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    const event = JSON.parse(body);

  switch (event.eventType) {
    case "checkout.completed":
      await handleCreemOrderSession(event.object);
      break;
      case "checkout.expired":
        await handleExpiredCheckout(event.object);
        break;
      case "subscription.paid":
        await handleSubscriptionPayment(event.object);
        break;
      default:
        console.log(`Unhandled event type: ${event.eventType}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook processing error:", error);
    return NextResponse.json({ error: "Processing failed" }, { status: 500 });
  }
}
```

## 📊 数据库变更

### 订单表结构更新

```sql
-- 新增 Creem.io 相关字段
ALTER TABLE orders ADD COLUMN creem_checkout_id VARCHAR(255);
ALTER TABLE orders ADD COLUMN creem_customer_id VARCHAR(255);
ALTER TABLE orders ADD COLUMN creem_subscription_id VARCHAR(255);
ALTER TABLE orders ADD COLUMN creem_order_id VARCHAR(255);
ALTER TABLE orders ADD COLUMN payment_provider VARCHAR(50) DEFAULT 'creem';

-- 添加索引优化查询
CREATE INDEX idx_orders_creem_checkout ON orders(creem_checkout_id);
CREATE INDEX idx_orders_creem_customer ON orders(creem_customer_id);
```

### Order 类型定义更新

```typescript
// types/order.d.ts
export interface Order {
  id: string;
  user_id: string;
  order_no: string;
  amount: number;
  currency: string;
  status: 'pending' | 'paid' | 'cancelled' | 'expired';
  credits: number;
  
  // Creem.io 相关字段
  creem_checkout_id?: string;
  creem_customer_id?: string;
  creem_subscription_id?: string;
  creem_order_id?: string;
  payment_provider: 'creem' | 'stripe';
  
  created_at: Date;
  updated_at: Date;
}
```

## 🔒 安全考虑

### 1. Webhook 签名验证

```typescript
// services/creem.ts
public verifyWebhookSignature(payload: string, signature: string): boolean {
  const crypto = require('crypto');
  
  // 移除 'sha256=' 前缀（如果存在）
  const cleanSignature = signature.replace('sha256=', '');
  
  const computedSignature = crypto
    .createHmac('sha256', this.config.webhookSecret)
    .update(payload, 'utf8')
    .digest('hex');

  // 使用时间安全的比较方法
  return crypto.timingSafeEqual(
    Buffer.from(cleanSignature, 'hex'),
    Buffer.from(computedSignature, 'hex')
  );
}
```

### 2. 环境变量保护

```typescript
// lib/env.ts - 环境变量验证
const requiredEnvVars = [
  'CREEM_API_KEY',
  'CREEM_WEBHOOK_SECRET',
  'FREE_PLAN_PRODUCT_ID',
  'PRO_PLAN_PRODUCT_ID'
];

requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
});
```

### 3. API 访问控制

```typescript
// middleware.ts - API 路由保护
export function middleware(request: NextRequest) {
  // 对敏感 API 路由进行访问控制
  if (request.nextUrl.pathname.startsWith('/api/creem-')) {
    // 验证请求来源、频率限制等
  }
}
```

## 🧪 测试指南

### 1. 本地开发测试

```bash
# 1. 启动开发服务器
npm run dev

# 2. 使用 ngrok 暴露本地端口（Webhook 测试必需）
npx ngrok http 3000

# 3. 更新 Creem.io Webhook URL 为 ngrok 地址
# https://xxxxxxxx.ngrok.io/api/creem-notify
```

### 2. 免费版测试（无需 Creem.io 配置）

```typescript
// 临时免费版处理逻辑
if (amount === 0 && product_id === "free") {
  // 直接处理免费版订单，跳过 Creem.io API
  const order = await createOrder({
    user_id: session.user.id,
    amount: 0,
    credits: 100, // 免费版赠送积分
    status: 'paid',
    payment_provider: 'creem'
  });
  
  return redirect('/creem-success?order_no=' + order.order_no);
}
```

### 3. 支付流程测试清单

```markdown
- [ ] 免费版直接处理（跳过支付）
- [ ] 付费版跳转到 Creem.io 支付页面
- [ ] 测试支付成功回调和积分增加
- [ ] 测试支付失败和过期处理
- [ ] 验证 Webhook 签名验证
- [ ] 检查订单状态同步
- [ ] 测试用户积分消费逻辑
```

### 4. 错误场景测试

```typescript
// 常见错误场景
const testCases = [
  'Product not found - 产品 ID 不存在',
  'Invalid signature - Webhook 签名错误', 
  'Network timeout - API 调用超时',
  'Database error - 订单创建失败',
  'User not found - 用户会话失效'
];
```

## 🚀 部署指南

### 1. 生产环境配置

```bash
# .env.production
CREEM_API_KEY=sk_live_your_production_api_key
CREEM_MODE=live
CREEM_WEBHOOK_SECRET=your_production_webhook_secret

# 更新产品 ID 为生产环境的产品
FREE_PLAN_PRODUCT_ID=prod_live_free_id
PRO_PLAN_PRODUCT_ID=prod_live_pro_id

# 生产域名配置
NEXT_PUBLIC_WEB_URL=https://our-service.org
```

### 2. Creem.io 生产环境配置

1. **切换到 Live Mode**：
   - 在 Creem.io Dashboard 切换到 Live Mode
   - 重新创建所有产品（Live 环境产品 ID 不同）
   - 获取新的 Live API Key

2. **更新 Webhook 配置**：
   ```
   生产 Webhook URL: https://our-service.org/api/creem-notify
   ```

3. **设置成功回调 URL**：
   ```
   Success URL: https://our-service.org/creem-success
   Cancel URL: https://our-service.org
   ```

### 3. 部署清单

```markdown
- [ ] 验证所有环境变量正确设置
- [ ] 确认生产环境产品 ID 已更新
- [ ] 测试生产环境 Webhook 接收
- [ ] 验证 HTTPS 证书有效
- [ ] 检查数据库连接和权限
- [ ] 测试完整支付流程
- [ ] 监控错误日志和支付成功率
```

## 🔧 故障排除

### 常见问题和解决方案

#### 1. Product not found 错误
```bash
# 检查步骤：
1. 确认产品 ID 正确复制（检查空格、特殊字符）
2. 确认当前环境模式（test/live）与产品创建环境一致
3. 验证 API Key 对应正确的环境

# 解决方案：
- 重新在对应环境创建产品
- 确认环境变量配置正确
```

#### 2. Webhook 签名验证失败
```typescript
// 调试 Webhook 签名
console.log('Received signature:', req.headers.get('creem-signature'));
console.log('Computed signature:', computedSignature);
console.log('Webhook secret:', process.env.CREEM_WEBHOOK_SECRET?.substring(0, 10) + '...');
```

#### 3. 支付跳转失败
```typescript
// 检查支付会话创建
const checkoutSession = await creem.createCheckoutSession({
  product_id,
  customer_email: user_email,
  success_url: `${process.env.NEXT_PUBLIC_WEB_URL}/creem-success`,
  cancel_url: `${process.env.NEXT_PUBLIC_WEB_URL}`,
});

console.log('Checkout session created:', checkoutSession);
```

#### 4. 订单状态不同步
```sql
-- 检查订单状态
SELECT order_no, status, creem_checkout_id, created_at 
FROM orders 
WHERE creem_checkout_id = 'checkout_session_id'
ORDER BY created_at DESC;
```

### 日志监控

```typescript
// 统一日志格式
const logEvent = (event: string, data: any) => {
  console.log(JSON.stringify({
    timestamp: new Date().toISOString(),
    event,
    data,
    environment: process.env.CREEM_MODE
  }));
};

// 使用示例
logEvent('payment_initiated', { product_id, user_email, amount });
logEvent('webhook_received', { eventType: event.eventType, checkout_id });
logEvent('order_created', { order_no, credits_added });
```

## 📈 监控和分析

### 1. 关键指标监控

```typescript
// 支付转化率分析
const analytics = {
  paymentInitiated: 0,    // 发起支付次数
  paymentCompleted: 0,    // 完成支付次数  
  paymentFailed: 0,       // 支付失败次数
  conversionRate: 0,      // 转化率
  averageOrderValue: 0,   // 平均订单价值
  totalRevenue: 0         // 总收入
};
```

### 2. 用户行为分析

```typescript
// 用户升级路径分析
const userJourney = {
  freeToBasic: 0,     // 免费版 → 基础版
  basicToPro: 0,      // 基础版 → 专业版
  proToElite: 0,      // 专业版 → 企业版
  churnRate: 0,       // 流失率
  retentionRate: 0    // 留存率
};
```

### 3. 成本效益分析

```typescript
// 根据定价策略计算利润率
const costAnalysis = {
  aiCostPerUser: 0,       // 人均 AI 成本
  infrastructureCost: 0,  // 基础设施成本
  grossMargin: 0,         // 毛利率
  netProfit: 0           // 净利润
};
```

## 🔄 从 Stripe 迁移

### 迁移策略

```markdown
1. **并行运行阶段**（1-2周）
   - 保留现有 Stripe 代码
   - 部署 Creem.io 集成
   - A/B 测试两套支付系统

2. **渐进迁移阶段**（2-4周）  
   - 新用户使用 Creem.io
   - 现有用户逐步迁移
   - 监控支付成功率和用户反馈

3. **完全切换阶段**（1周）
   - 停用 Stripe 支付入口
   - 保留 Stripe 数据查询功能
   - 清理冗余代码

4. **清理优化阶段**（1-2周）
   - 移除 Stripe 相关代码
   - 优化 Creem.io 集成
   - 完善监控和报警
```

### 数据迁移

```sql
-- 标记支付方式来源
UPDATE orders
SET payment_provider = 'stripe' 
WHERE created_at < '2024-01-01' AND payment_provider IS NULL;

-- 分析不同支付方式的表现
SELECT 
  payment_provider,
  COUNT(*) as order_count,
  SUM(amount) as total_revenue,
  AVG(amount) as avg_order_value
FROM orders 
WHERE status = 'paid'
GROUP BY payment_provider;
```

## 📞 技术支持

### 开发资源
- [Creem.io 官方文档](https://docs.creem.io/)
- [API 参考文档](https://docs.creem.io/api)
- [Webhook 事件说明](https://docs.creem.io/webhooks)

### 项目文档
- `doc/Creem产品配置指南.md` - 产品创建详细步骤
- `doc/支付问题解决方案.md` - 常见问题快速解决
- `doc/定价策略.md` - 定价策略和成本分析

### 联系方式
- **技术支持**: 项目开发团队
- **紧急问题**: 查看项目 README 联系方式
- **Creem.io 支持**: <EMAIL>

---

**最后更新**: 2024年12月

**版本**: v2.0 - 完整生产就绪版本

**维护者**: 开发团队
