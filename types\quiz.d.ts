// 测验相关类型定义

export interface QuizOption {
  id: string;
  text: string;
  isCorrect: boolean;
}

export interface QuizQuestion {
  id: string;
  text: string;
  type: 'mcq' | 'true_false' | 'short_answer' | 'essay';
  options?: QuizOption[];
  correctAnswer?: string;
  explanation?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  points?: number;
  sourceText?: string; // 来源PDF的相关文本
}

export interface Quiz {
  id: string;
  title: string;
  description?: string;
  questions: QuizQuestion[];
  totalQuestions: number;
  totalPoints: number;
  estimatedTime?: number; // 预计完成时间（分钟）
  difficulty?: 'easy' | 'medium' | 'hard';
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
  sourceFile?: {
    name: string;
    type: string;
    size: number;
  };
}

export interface QuizGenerationOptions {
  questionCount: number;
  questionTypes: ('mcq' | 'true_false' | 'short_answer' | 'essay')[];
  difficulty: 'easy' | 'medium' | 'hard' | 'mixed';
  language: 'zh' | 'en';
  includeExplanations: boolean;
  focusAreas?: string[]; // 重点关注的主题
}

export interface QuizGenerationResult {
  quiz: Quiz;
  processingTime: number;
  extractedText: string;
  confidence: number; // AI生成的置信度
  warnings?: string[];
}

export interface QuizExportOptions {
  format: 'json' | 'pdf' | 'docx' | 'txt' | 'csv';
  includeAnswers: boolean;
  includeExplanations: boolean;
  template?: string;
}

export interface QuizAttempt {
  id: string;
  quizId: string;
  userId?: string;
  answers: Record<string, string | string[]>;
  score: number;
  totalPoints: number;
  percentage: number;
  timeSpent: number; // 秒
  startedAt: Date;
  completedAt: Date;
  isCompleted: boolean;
}

export interface QuizStats {
  totalAttempts: number;
  averageScore: number;
  averageTime: number;
  questionStats: {
    questionId: string;
    correctRate: number;
    averageTime: number;
  }[];
}
