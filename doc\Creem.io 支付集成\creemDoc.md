https://docs.creem.io/introduction
Get Started
Introduction
Quickstart
Standard Integration
Api Keys
Test Mode
Learn
Checkout Session
Introduction
Return URLs
Webhooks
Introduction
Event Types
Verify Webhook Requests
Billing
Customers
Integrations
AI Negotiation bot
Affilliates
Product Features
Introduction
Licenses
Private Notes
File Downloads
Custom Fields
Finance
Payments
Payouts
Merchant of Record
Refunds and Chargebacks
For Customers and Buyers
Why did Creem charge me
How to cancel a subscription
How to update payment method
FAQ
Supported Countries
Prohibited Products
Account Reviews

----
Get Started
Introduction
Creem is the payment partner you always deserved, we strive for simplicity and straightforwardness on our APIs.

​
Quickstart
The first step to start using Creem is to create an account and get your API key.

After that, feel free to explore our API reference for more details. Or to jump start into our straightforward tutorial.

From 0 to Hero
We’ll guide you through the process of receiving your first payment in 10 minutes.

Webhooks
Understand how to receive updates on your application automatically.

​
Guides
Create customers and subscriptions, manage your products, and much more. Check out our guides to get the most out of Creem.

No-Code Payments
Receive payments without any code or integration.

Standard Integration
Create checkout sessions dynamically in your app
---
Get Started
Quickstart
Learn how to receive your first payment in under 10 minutes

​
Prerequisites
To get the most out of this guide, you’ll need to:

Create an account on Creem.io
​
1. Create a product

Go over to the products tab and create a product. You can add a name, description, and price to your product. Optionally you can also add a picture to your product that will be shown to users.


Product page


Adding product details

​
2. Copy the payment link from the product
After successfully creating your product, you can copy the payment link by clicking on the product Share button. Simply send this link to your users and they will be able to pay you instantly.


​
More use cases
If you are not planning to do a no-code integration, we strongly encourage you to check out our other guides.

Create checkout-sessions and prices dynamically, use webhooks to receive updates on your application automatically, and much more. Check out our guides to get the most out of Creem.
---

Get Started
Standard Integration
Learn how to receive payments on your application

​
Prerequisites
To get the most out of this guide, you’ll need to:

Create an account on Creem.io
Have your API key ready
​
1. Create a product
Go over to the products tab and create a product. You can add a name, description, and price to your product. Optionally you can also add a picture to your product that will be shown to users.


Product page


Adding product details

​
2 Create a checkout session
Once your product is created, you can copy the product ID by clicking on the product options and selecting “Copy ID”.

Now grab your api-key and create a checkout session by sending a POST request to the following endpoint:


getCheckout.sh

getCheckout.js

Copy
    const redirectUrl = await axios.post(
      `https://api.creem.io/v1/checkouts`,
        {
          product_id: 'prod_6tW66i0oZM7w1qXReHJrwg',
        },
        {
          headers: { "x-api-key": `creem_123456789` },
        },
    );
Read more about all attributes you can pass to a checkout sesssion here

​
3. Redirect user to checkout url
Once you have created a checkout session, you will receive a checkout URL in the response.

Redirect the user to this URL and that is it! You have successfully created a checkout session and received your first payment!


Track payments with a request ID

When creating a checkout-session, you can optionally add a request_id parameter to track the payment. This parameter will be sent back to you in the response and in the webhook events. Use this parameter to track the payment or user in your system.


Set a success URL on the checkout session

After successfully completing the payment, the user will be automatically redirected to the URL you have set on the product creation. You can bypass this setting by setting a success URL on the checkout session request by adding the success_url parameter. The user will always be redirected with the following query parameters:

session_id: The ID of the checkout session
product_id: The ID of the product
status: The status of the payment
request_id: The request ID of the payment that you optionally have sent
​
4. Receive payment data on your Return URL
A return URL will always contain the following query parameters, and will look like the following:

https://yourwebsite.com/your-return-path?checkout_id=ch_1QyIQDw9cbFWdA1ry5Qc6I&order_id=ord_4ucZ7Ts3r7EhSrl5yQE4G6&customer_id=cust_2KaCAtu6l3tpjIr8Nr9XOp&subscription_id=sub_ILWMTY6uBim4EB0uxK6WE&product_id=prod_6tW66i0oZM7w1qXReHJrwg&signature=044bd1691d254c4ad4b31b7f246330adf09a9f07781cd639979a288623f4394c?

You can read more about Return Urls here.

Query parameter	Description
checkout_id	The ID of the checkout session created for this payment.
order_id	The ID of the order created after successful payment.
customer_id	The customer ID, based on the email that executed the successful payment.
subscription_id	The subscription ID of the product.
product_id	The product ID that the payment is related to.
request_id	Optional The request ID you provided when creating this checkout session.
signature	All previous parameters signed by creem using your API-key, verifiable by you.
We also encourage reading on how you can verify Creem signature on return URLs here.

​
Expanding your integration
You can also use webhooks to check payment data dynamically in your application, without the need to wait for the return URLs, or have the user redirected to your application website.
---
Api Keys
Learn how to generate and use your API key

​
What is an API Key
API Keys are secret tokens used to authenticate your requests. They are unique to your account and should be kept confidential.

​
Create API Key
Go over to the dashboard and login to your account.

On the top navbar, navigate to the “Developers” section.

Click on the eye icon to reveal your API key.

Copy your API key and keep it safe.

----
Test Mode
Simulate payments and any functionality in a test environment

​
Activating Test Mode
To use the test environment, simply click on the test mode button on the top navbar of the dashboard.


All functionality on Creem will now be in test mode. You can create products, checkout sessions, and receive payments without any real money being involved.

Webhooks and API keys that you will see on the Developer tab will also be exclusively for the test environment.

​
Using Test APIs
You can test and use any workflows for the test environment by using the test API endpoint:


Copy
https://test-api.creem.io
​
Testing Payments
Use the test card 4242 4242 4242 4242 with any expiration and CVV
---
Checkout Session
Introduction
Create checkout sessions for each payment and user independently.


​
Why use a checkout session?
With checkout sessions generated dynamically instead of just using a product payment link, you are able to pass a request_id to the session.

This allows you to track the payment status and the user that made the payment in your system in a more organized way, and gives you the flexibility to track that payment with any ID you choose instead of relying on Creem IDs.

​
Create a checkout session
You can create a checkout session with a product ID

You can find a product ID by going to the products tab and clicking on the product options and selecting “Copy ID”.

Additionally, you can pass a request_id to the session to track the payment in your system. You will also need the API key to authenticate the request.


getCheckout.js

getCheckout.sh

Copy
    const redirectUrl = await axios.post(
      `https://api.creem.io/v1/checkouts`,
        {
          product_id: 'prod_6tW66i0oZM7w1qXReHJrwg',
          request_id: 'your-request-id',
        },
        {
          headers: { "x-api-key": `creem_123456789` },
        },
    );
The above request will return a checkout session object with a checkout_url that you can use to redirect the user to the payment page.

Any payments made with this checkout session will have the request_id you provided on the Redirect URL, as well as the webhook event.

​
Metadata
The request_id is only returned in the checkout.completed webhook (which is very useful for one-time payments), but it’s not sent with every new subscription transaction.

To make things easier, we also allow you to pass metadata in a checkoutSession with or without the request_id. This metadata will be saved in the Subscription object and returned with every subsequent webhook.


getCheckout.js

getCheckout.sh

Copy
    const redirectUrl = await axios.post(
      `https://api.creem.io/v1/checkouts`,
        {
          "request_id": "your-request-id",
          "product_id": "prod_your-product-id",
          "metadata": {
            "userId": "my_internal_customer_id",
            "any_key": "any_value"
          }
        },
        {
          headers: { "x-api-key": `creem_123456789` },
        },
    );
​
Success URL
You can pass a custom success_url for each checkout_session, which will override the success_url set on the product.

This allows you to dynamically redirect users to custom pages after each payment (useful for directing users to their specific account resources after payment).


getCheckout.js

getCheckout.sh

Copy
    const redirectUrl = await axios.post(
      `https://api.creem.io/v1/checkouts`,
        {
          "success_url": "https://example.com",
          "product_id": "prod_your-product-id",
        },
        {
          headers: { "x-api-key": `creem_123456789` },
        },
    );
​
Customer Email
You can pass a customer.email directly in the checkout session. This email will be pre-filled for the user on the checkout session page and cannot be changed.

This is useful if you want to ensure that the user completes the payment using the email they registered on your platform.


getCheckout.js

getCheckout.sh

Copy
    const redirectUrl = await axios.post(
      `https://api.creem.io/v1/checkouts`,
        {
          "customer": {
            "email": "<EMAIL>"
          },
          "product_id": "prod_your-product-id",
        },
        {
          headers: { "x-api-key": `creem_123456789` },
        },
    );
​
Discount Codes
You can pass a discount_code directly in the checkout session. This discount code will be pre-filled for the user on the checkout session page.


getCheckout.js

getCheckout.sh

Copy
    const redirectUrl = await axios.post(
      `https://api.creem.io/v1/checkouts`,
        {
          "product_id": "prod_your-product-id",
          "discount_code": "BF200XX",
        },
        {
          headers: { "x-api-key": `creem_123456789` },
        },
    );
​
Seat Based Billing
You can pass a units amount directly in the checkout session. The product price will be used as the base price for one seat/unit of the product and the checkout session will reflect the (base price x units) to be charged.


getCheckout.js

getCheckout.sh

Copy
    const redirectUrl = await axios.post(
      `https://api.creem.io/v1/checkouts`,
        {
          "product_id": "prod_your-product-id",
          "units": 2,
        },
        {
          headers: { "x-api-key": `creem_123456789` },
        },
    );
    ---
    Checkout Session
Return URLs
Understand how to redirect users back to your website after a successful payment.

​
What is a Return/Redirect URL?
Return and Redirect URLs, are urls that your customer will be redirected to, after a successful payment. They contain important information signed by creem, that you can use to verify the payment and the user.

Using these URLs, you can create a seamless experience for your users, by redirecting them back to your website after a successful payment.

You have the optionality to use the information in the URL query parameters, or to use webhooks to receive updates on your application automatically, or both.

​
How to set a Return/Redirect URL

Option 1: Set a success URL on the product creation.


Option 2: Set a success URL when creating a checkout session

​
What is included on the Return URL?
A return URL will always contain the following query parameters, and will look like the following:

https://yourwebsite.com?checkout_id=ch_1QyIQDw9cbFWdA1ry5Qc6I&order_id=ord_4ucZ7Ts3r7EhSrl5yQE4G6&customer_id=cust_2KaCAtu6l3tpjIr8Nr9XOp&subscription_id=sub_ILWMTY6uBim4EB0uxK6WE&product_id=prod_6tW66i0oZM7w1qXReHJrwg&signature=044bd1691d254c4ad4b31b7f246330adf09a9f07781cd639979a288623f4394c?

Query parameter	Description
checkout_id	The ID of the checkout session created for this payment.
order_id	The ID of the order created after successful payment.
customer_id	The customer ID, based on the email that executed the successful payment.
subscription_id	The subscription ID of the product.
product_id	The product ID that the payment is related to.
request_id	Optional The request ID you provided when creating this checkout session.
signature	All previous parameters signed by creem using your API-key, verifiable by you.
​
How to verify Creem signature?
To verify the signature, you can use the following code snippet:


Copy
export interface RedirectParams {
  request_id?: string | null;
  checkout_id?: string | null;
  order_id?: string | null;
  customer_id?: string | null;
  subscription_id?: string | null;
  product_id?: string | null;
}

  private generateSignature(params: RedirectParams, apiKey: string): string {
    const data = Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .concat(`salt=${apiKey}`)
      .join('|');
    return crypto.createHash('sha256').update(data).digest('hex');
  }
In summary, concatenate all parameters and the salt (your API-key) with a | separator, and hash it using SHA256. This will generate a signature that you can compare with the signature provided in the URL.
---
Webhooks
Introduction
Use webhooks to notify your application about payment events.”

​
What is a webhook?
Creem uses webhooks to push real-time notifications to you about your payments and subscriptions. All webhooks use HTTPS and deliver a JSON payload that can be used by your application. You can use webhook feeds to do things like:

Automatically enable access to a user after a successful payment
Automatically remove access to a user after a canceled subscription
Confirm that a payment has been received by the same customer that initiated it.
In case webhooks are not successfully received by your endpoint, creem automatically retries to send the request with a progressive backoff period of 30 seconds, 1 minute, 5 minutes and 1 hour.

​
Steps to receive a webhook
You can start receiving real-time events in your app using the steps:

Create a local endpoint to receive requests
Register your development webhook endpoint on the Developers tab of the Creem dashboard
Test that your webhook endpoint is working properly using the test environment
Deploy your webhook endpoint to production
Register your production webhook endpoint on Creem live dashboard
​
1. Create a local endpoint to receive requests
In your local application, create a new route that can accept POST requests.

For example, you can add an API route on Next.js:


Copy
import type { NextApiRequest, NextApiResponse } from 'next';

export default (req: NextApiRequest, res: NextApiResponse) => {
  if (req.method === 'POST') {
    const payload = req.body;
    console.log(payload);
    res.status(200);
  }
};
On receiving an event, you should respond with an HTTP 200 OK to signal to Creem that the event was successfully delivered.

​
2. Register your development webhook endpoint
Register your publicly accessible HTTPS URL in the Creem dashboard.

You can create a tunnel to your localhost server using a tool like ngrok. For example: https://8733-191-204-177-89.sa.ngrok.io/api/webhooks


​
3. Test that your webhook endpoint is working properly
Create a few test payments to check that your webhook endpoint is receiving the events.

​
4. Deploy your webhook endpoint
After you’re done testing, deploy your webhook endpoint to production.

​
5. Register your production webhook endpoint
Once your webhook endpoint is deployed to production, you can register it in the Creem dashboard.
---
Webhooks
Event Types
List of supported event types and their payloads.

​
checkout.completed
A checkout session was completed, returning all the information about the payment and the order created.


Sample Request Body


Copy
{
  "id": "evt_5WHHcZPv7VS0YUsberIuOz",
  "eventType": "checkout.completed",
  "created_at": *************,
  "object": {
    "id": "ch_4l0N34kxo16AhRKUHFUuXr",
    "object": "checkout",
    "request_id": "my-request-id",
    "order": {
      "id": "ord_4aDwWXjMLpes4Kj4XqNnUA",
      "customer": "cust_1OcIK1GEuVvXZwD19tjq2z",
      "product": "prod_d1AY2Sadk9YAvLI0pj97f",
      "amount": 1000,
      "currency": "EUR",
      "status": "paid",
      "type": "recurring",
      "created_at": "2024-10-12T11:58:33.097Z",
      "updated_at": "2024-10-12T11:58:33.097Z",
      "mode": "local"
    },
    "product": {
      "id": "prod_d1AY2Sadk9YAvLI0pj97f",
      "name": "Monthly",
      "description": "Monthly",
      "image_url": null,
      "price": 1000,
      "currency": "EUR",
      "billing_type": "recurring",
      "billing_period": "every-month",
      "status": "active",
      "tax_mode": "exclusive",
      "tax_category": "saas",
      "default_success_url": "",
      "created_at": "2024-10-11T11:50:00.182Z",
      "updated_at": "2024-10-11T11:50:00.182Z",
      "mode": "local"
    },
    "customer": {
      "id": "cust_1OcIK1GEuVvXZwD19tjq2z",
      "object": "customer",
      "email": "<EMAIL>",
      "name": "Tester Test",
      "country": "NL",
      "created_at": "2024-10-11T09:16:48.557Z",
      "updated_at": "2024-10-11T09:16:48.557Z",
      "mode": "local"
    },
    "subscription": {
      "id": "sub_6pC2lNB6joCRQIZ1aMrTpi",
      "object": "subscription",
      "product": "prod_d1AY2Sadk9YAvLI0pj97f",
      "customer": "cust_1OcIK1GEuVvXZwD19tjq2z",
      "collection_method": "charge_automatically",
      "status": "active",
      "canceled_at": null,
      "created_at": "2024-10-12T11:58:45.425Z",
      "updated_at": "2024-10-12T11:58:45.425Z",
      "metadata": {
        "custom_data": "mycustom data",
        "internal_customer_id": "internal_customer_id"
      },
      "mode": "local"
    },
    "custom_fields": [],
    "status": "completed",
    "metadata": {
      "custom_data": "mycustom data",
      "internal_customer_id": "internal_customer_id"
    },
    "mode": "local"
  }
}
​
subscription.active
Received when a new subscription is created, the payment was successful and Creem collected the payment creating a new subscription object in your account. Use only for synchronization, we encourage using subscription.paid for activating access.


Sample Request Body


Copy
{
  "id": "evt_6EptlmjazyGhEPiNQ5f4lz",
  "eventType": "subscription.active",
  "created_at": *************,
  "object": {
      "id": "sub_21lfZb67szyvMiXnm6SVi0",
      "object": "subscription",
      "product": {
          "id": "prod_AnVJ11ujp7x953ARpJvAF",
          "name": "My Product - Product 01",
          "description": "Test my product",
          "image_url": null,
          "price": 10000,
          "currency": "EUR",
          "billing_type": "recurring",
          "billing_period": "every-month",
          "status": "active",
          "tax_mode": "inclusive",
          "tax_category": "saas",
          "default_success_url": "",
          "created_at": "2024-09-16T16:12:09.813Z",
          "updated_at": "2024-09-16T16:12:09.813Z",
          "mode": "local"
      },
      "customer": {
          "id": "cust_3biFPNt4Cz5YRDSdIqs7kc",
          "object": "customer",
          "email": "<EMAIL>",
          "name": "Tester Test",
          "country": "SE",
          "created_at": "2024-09-16T16:13:39.265Z",
          "updated_at": "2024-09-16T16:13:39.265Z",
          "mode": "local"
      },
      "collection_method": "charge_automatically",
      "status": "active",
      "canceled_at": "2024-09-16T19:40:41.984Z",
      "created_at": "2024-09-16T19:40:41.984Z",
      "updated_at": "2024-09-16T19:40:42.121Z",
      "mode": "local"
  }
}
​
subscription.paid
A subscription transaction was paid by the customer


Sample Request Body


Copy
{
  "id": "evt_21mO1jWmU2QHe7u2oFV7y1",
  "eventType": "subscription.paid",
  "created_at": 1728734327355,
  "object": {
    "id": "sub_6pC2lNB6joCRQIZ1aMrTpi",
    "object": "subscription",
    "product": {
      "id": "prod_d1AY2Sadk9YAvLI0pj97f",
      "name": "Monthly",
      "description": "Monthly",
      "image_url": null,
      "price": 1000,
      "currency": "EUR",
      "billing_type": "recurring",
      "billing_period": "every-month",
      "status": "active",
      "tax_mode": "exclusive",
      "tax_category": "saas",
      "default_success_url": "",
      "created_at": "2024-10-11T11:50:00.182Z",
      "updated_at": "2024-10-11T11:50:00.182Z",
      "mode": "local"
    },
    "customer": {
      "id": "cust_1OcIK1GEuVvXZwD19tjq2z",
      "object": "customer",
      "email": "<EMAIL>",
      "name": "Tester Test",
      "country": "NL",
      "created_at": "2024-10-11T09:16:48.557Z",
      "updated_at": "2024-10-11T09:16:48.557Z",
      "mode": "local"
    },
    "collection_method": "charge_automatically",
    "status": "active",
    "last_transaction_id": "tran_5yMaWzAl3jxuGJMCOrYWwk",
    "last_transaction_date": "2024-10-12T11:58:47.109Z",
    "next_transaction_date": "2024-11-12T11:58:38.000Z",
    "current_period_start_date": "2024-10-12T11:58:38.000Z",
    "current_period_end_date": "2024-11-12T11:58:38.000Z",
    "canceled_at": null,
    "created_at": "2024-10-12T11:58:45.425Z",
    "updated_at": "2024-10-12T11:58:45.425Z",
    "metadata": {
      "custom_data": "mycustom data",
      "internal_customer_id": "internal_customer_id"
    },
    "mode": "local"
  }
}
​
subscription.canceled
The subscription was canceled by the merchant or by the customer.


Sample Request Body


Copy
{
  "id": "evt_2iGTc600qGW6FBzloh2Nr7",
  "eventType": "subscription.canceled",
  "created_at": 1728734337932,
  "object": {
    "id": "sub_6pC2lNB6joCRQIZ1aMrTpi",
    "object": "subscription",
    "product": {
      "id": "prod_d1AY2Sadk9YAvLI0pj97f",
      "name": "Monthly",
      "description": "Monthly",
      "image_url": null,
      "price": 1000,
      "currency": "EUR",
      "billing_type": "recurring",
      "billing_period": "every-month",
      "status": "active",
      "tax_mode": "exclusive",
      "tax_category": "saas",
      "default_success_url": "",
      "created_at": "2024-10-11T11:50:00.182Z",
      "updated_at": "2024-10-11T11:50:00.182Z",
      "mode": "local"
    },
    "customer": {
      "id": "cust_1OcIK1GEuVvXZwD19tjq2z",
      "object": "customer",
      "email": "<EMAIL>",
      "name": "Tester Test",
      "country": "NL",
      "created_at": "2024-10-11T09:16:48.557Z",
      "updated_at": "2024-10-11T09:16:48.557Z",
      "mode": "local"
    },
    "collection_method": "charge_automatically",
    "status": "canceled",
    "last_transaction_id": "tran_5yMaWzAl3jxuGJMCOrYWwk",
    "last_transaction_date": "2024-10-12T11:58:47.109Z",
    "current_period_start_date": "2024-10-12T11:58:38.000Z",
    "current_period_end_date": "2024-11-12T11:58:38.000Z",
    "canceled_at": "2024-10-12T11:58:57.813Z",
    "created_at": "2024-10-12T11:58:45.425Z",
    "updated_at": "2024-10-12T11:58:57.827Z",
    "metadata": {
      "custom_data": "mycustom data",
      "internal_customer_id": "internal_customer_id"
    },
    "mode": "local"
  }
}
​
subscription.expired
The subscription was expired, given that the current_end_period has been reached without a new payment. Payment retries can happen at this stage, and the subscription status will be terminal only when status is changed to canceled.


Sample Request Body


Copy
{
  "id": "evt_V5CxhipUu10BYonO2Vshb",
  "eventType": "subscription.expired",
  "created_at": 1734463872058,
  "object": {
      "id": "sub_7FgHvrOMC28tG5DEemoCli",
      "object": "subscription",
      "product": {
          "id": "prod_3ELsC3Lt97orn81SOdgQI3",
          "name": "Subs",
          "description": "Subs",
          "image_url": null,
          "price": 1200,
          "currency": "EUR",
          "billing_type": "recurring",
          "billing_period": "every-year",
          "status": "active",
          "tax_mode": "exclusive",
          "tax_category": "saas",
          "default_success_url": "",
          "created_at": "2024-12-11T17:33:32.186Z",
          "updated_at": "2024-12-11T17:33:32.186Z",
          "mode": "local"
      },
      "customer": {
          "id": "cust_3y4k2CELGsw7n9Eeeiw2hm",
          "object": "customer",
          "email": "<EMAIL>",
          "name": "Alec Erasmus",
          "country": "NL",
          "created_at": "2024-12-09T16:09:20.709Z",
          "updated_at": "2024-12-09T16:09:20.709Z",
          "mode": "local"
      },
      "collection_method": "charge_automatically",
      "status": "active",
      "last_transaction_id": "tran_6ZeTvMqMkGdAIIjw5aAcnh",
      "last_transaction_date": "2024-12-16T12:40:12.658Z",
      "next_transaction_date": "2025-12-16T12:39:47.000Z",
      "current_period_start_date": "2024-12-16T12:39:47.000Z",
      "current_period_end_date": "2024-12-16T12:39:47.000Z",
      "canceled_at": null,
      "created_at": "2024-12-16T12:40:05.058Z",
      "updated_at": "2024-12-16T12:40:05.058Z",
      "mode": "local"
  }
}
​
refund.created
A refund was created by the merchant


Sample Request Body


Copy
{
"id": "evt_61eTsJHUgInFw2BQKhTiPV",
"eventType": "refund.created",
"created_at": 1728734351631,
"object": {
  "id": "ref_3DB9NQFvk18TJwSqd0N6bd",
  "object": "refund",
  "status": "succeeded",
  "refund_amount": 1210,
  "refund_currency": "EUR",
  "reason": "requested_by_customer",
  "transaction": {
    "id": "tran_5yMaWzAl3jxuGJMCOrYWwk",
    "object": "transaction",
    "amount": 1000,
    "amount_paid": 1210,
    "currency": "EUR",
    "type": "invoice",
    "tax_country": "NL",
    "tax_amount": 210,
    "status": "refunded",
    "refunded_amount": 1210,
    "order": "ord_4aDwWXjMLpes4Kj4XqNnUA",
    "subscription": "sub_6pC2lNB6joCRQIZ1aMrTpi",
    "description": "Subscription payment",
    "period_start": 1728734318000,
    "period_end": 1731412718000,
    "created_at": 1728734327109,
    "mode": "local"
  },
  "subscription": {
    "id": "sub_6pC2lNB6joCRQIZ1aMrTpi",
    "object": "subscription",
    "product": "prod_d1AY2Sadk9YAvLI0pj97f",
    "customer": "cust_1OcIK1GEuVvXZwD19tjq2z",
    "collection_method": "charge_automatically",
    "status": "canceled",
    "last_transaction_id": "tran_5yMaWzAl3jxuGJMCOrYWwk",
    "last_transaction_date": "2024-10-12T11:58:47.109Z",
    "current_period_start_date": "2024-10-12T11:58:38.000Z",
    "current_period_end_date": "2024-11-12T11:58:38.000Z",
    "canceled_at": "2024-10-12T11:58:57.813Z",
    "created_at": "2024-10-12T11:58:45.425Z",
    "updated_at": "2024-10-12T11:58:57.827Z",
    "metadata": {
      "custom_data": "mycustom data",
      "internal_customer_id": "internal_customer_id"
    },
    "mode": "local"
  },
  "checkout": {
    "id": "ch_4l0N34kxo16AhRKUHFUuXr",
    "object": "checkout",
    "request_id": "my-request-id",
    "custom_fields": [],
    "status": "completed",
    "metadata": {
      "custom_data": "mycustom data",
      "internal_customer_id": "internal_customer_id"
    },
    "mode": "local"
  },
  "order": {
    "id": "ord_4aDwWXjMLpes4Kj4XqNnUA",
    "customer": "cust_1OcIK1GEuVvXZwD19tjq2z",
    "product": "prod_d1AY2Sadk9YAvLI0pj97f",
    "amount": 1000,
    "currency": "EUR",
    "status": "paid",
    "type": "recurring",
    "created_at": "2024-10-12T11:58:33.097Z",
    "updated_at": "2024-10-12T11:58:33.097Z",
    "mode": "local"
  },
  "customer": {
    "id": "cust_1OcIK1GEuVvXZwD19tjq2z",
    "object": "customer",
    "email": "<EMAIL>",
    "name": "Tester Test",
    "country": "NL",
    "created_at": "2024-10-11T09:16:48.557Z",
    "updated_at": "2024-10-11T09:16:48.557Z",
    "mode": "local"
  },
  "created_at": 1728734351525,
  "mode": "local"
}
}
​
subscription.update
A subscription object was updated


Sample Request Body


Copy
{
"id": "evt_5pJMUuvqaqvttFVUvtpY32",
"eventType": "subscription.update",
"created_at": 1737890536421,
"object": {
  "id": "sub_2qAuJgWmXhXHAuef9k4Kur",
  "object": "subscription",
  "product": {
    "id": "prod_1dP15yoyogQe2seEt1Evf3",
    "name": "Monthly Sub",
    "description": "Test Test",
    "image_url": null,
    "price": 1000,
    "currency": "EUR",
    "billing_type": "recurring",
    "billing_period": "every-month",
    "status": "active",
    "tax_mode": "exclusive",
    "tax_category": "saas",
    "default_success_url": "",
    "created_at": "2025-01-26T11:17:16.082Z",
    "updated_at": "2025-01-26T11:17:16.082Z",
    "mode": "local"
  },
  "customer": {
    "id": "cust_2fQZKKUZqtNhH2oDWevQkW",
    "object": "customer",
    "email": "<EMAIL>",
    "name": "John Doe",
    "country": "NL",
    "created_at": "2025-01-26T11:18:24.071Z",
    "updated_at": "2025-01-26T11:18:24.071Z",
    "mode": "local"
  },
  "items": [
    {
      "object": "subscription_item",
      "id": "sitem_3QWlqRbAat2eBRakAxFtt9",
      "product_id": "prod_5jnudVkLGZWF4AqMFBs5t5",
      "price_id": "pprice_4W0mJK6uGiQzHbVhfaFTl1",
      "units": 1,
      "created_at": "2025-01-26T11:20:40.296Z",
      "updated_at": "2025-01-26T11:20:40.296Z",
      "mode": "local"
    }
  ],
  "collection_method": "charge_automatically",
  "status": "active",
  "current_period_start_date": "2025-01-26T11:20:36.000Z",
  "current_period_end_date": "2025-02-26T11:20:36.000Z",
  "canceled_at": null,
  "created_at": "2025-01-26T11:20:40.292Z",
  "updated_at": "2025-01-26T11:22:16.388Z",
  "mode": "local"
}
}
​
subscription.trialing
A subscription started a trial period


Sample Request Body


Copy

{
"id": "evt_2ciAM8ABYtj0pVueeJPxUZ",
"eventType": "subscription.trialing",
"created_at": 1739963911073,
"object": {
  "id": "sub_dxiauR8zZOwULx5QM70wJ",
  "object": "subscription",
  "product": {
    "id": "prod_3kpf0ZdpcfsSCQ3kDiwg9m",
    "name": "trail",
    "description": "asdfasf",
    "image_url": null,
    "price": 1100,
    "currency": "EUR",
    "billing_type": "recurring",
    "billing_period": "every-month",
    "status": "active",
    "tax_mode": "exclusive",
    "tax_category": "saas",
    "default_success_url": "",
    "created_at": "2025-02-19T11:18:07.570Z",
    "updated_at": "2025-02-19T11:18:07.570Z",
    "mode": "test"
  },
  "customer": {
    "id": "cust_4fpU8kYkQmI1XKBwU2qeME",
    "object": "customer",
    "email": "<EMAIL>",
    "name": "Alec Erasmus",
    "country": "NL",
    "created_at": "2024-11-07T23:21:11.763Z",
    "updated_at": "2024-11-07T23:21:11.763Z",
    "mode": "test"
  },
  "items": [
    {
      "object": "subscription_item",
      "id": "sitem_1xbHCmIM61DHGRBCFn0W1L",
      "product_id": "prod_3kpf0ZdpcfsSCQ3kDiwg9m",
      "price_id": "pprice_517h9CebmM3P079bGAXHnE",
      "units": 1,
      "created_at": "2025-02-19T11:18:30.690Z",
      "updated_at": "2025-02-19T11:18:30.690Z",
      "mode": "test"
    }
  ],
  "collection_method": "charge_automatically",
  "status": "trialing",
  "current_period_start_date": "2025-02-19T11:18:25.000Z",
  "current_period_end_date": "2025-02-26T11:18:25.000Z",
  "canceled_at": null,
  "created_at": "2025-02-19T11:18:30.674Z",
  "updated_at": "2025-02-19T11:18:30.674Z",
  "mode": "test"
  }
}
----
Webhooks
Verify Webhook Requests
How to verify Creem signature on webhook objects.

​
How to verify Creem signature?
Creem signature is sent in the creem-signature header of the webhook request. The signature is generated using the HMAC-SHA256 algorithm with the webhook secret as the key, and the request payload as the message.


Sample Webhook Header


Copy
{
'creem-signature': 'dd7bdd2cf1f6bac6e171c6c508c157b7cd3cc1fd196394277fb59ba0bdd9b87b'
}
To verify the signature, you need to generate the signature using the same algorithm and compare it with the signature sent in the header. If the two signatures match, the request is authentic.

You can find your webhook secret on the Developers>Webhook page.


To generate the signature, you can use the following code snippet:


Copy
import * as crypto from 'crypto';

  generateSignature(payload: string, secret: string): string {
    const computedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
    return computedSignature;
  }
In the code snippet above, the payload is the request body, and the secret is the webhook secret. Simply compare the generated Signature with the one received on the header to complete the verification process.
---
Billing
Seat Based Billing
Seat Based Billing in Creem enables you to charge customers based on the number of seats or users they need, supporting both one-time payments and subscription models. This documentation explains how to implement and manage seat-based billing for your products.

​
Understanding Seat Based Billing
Seat Based Billing allows you to set a per-seat price for your product and let customers purchase multiple seats. This is particularly useful for team-based products, enterprise software, or any service where pricing scales with the number of users.

Seat Based Billing in Creem works for both One Time Payments and Subscriptions seamlessly without any special setup.

​
Key Concepts
​
Per-Seat Pricing
In Seat Based Billing:

Base Price: Set in the Creem dashboard as the price per individual seat
Units: Represents the number of seats being purchased
Total Price: Automatically calculated as (Base Price × Units)
​
Implementation
To implement Seat Based Billing, you’ll need to:

Create a product in your Creem dashboard where the price is the base seat price
Generate a checkout session with the desired number of seats
Direct your customer to the checkout URL
​
Code Example

Copy
const seatBasedCheckout = await axios.post(
  `https://api.creem.io/v1/checkouts`,
  {
    product_id: 'prod_your-product-id',
    units: 5, // Number of seats to purchase
  },
  {
    headers: { "x-api-key": `creem_123456789` },
  }
);
​
Managing Seat Changes
You can manage seat quantities for your customers in subscriptions by:

Adding seats: Modify the subscription through the dashboard or update subscription API
Reducing seats: Modify the subscription through the dashboard or update subscription API
​
Update Subscription API
If you wish to add or reduce the amount of seats in a given subscription, you can use the subscription API to make changes. Usually, it is great practice for your system to store the subscription ID of a given customer. With the subscription ID, we can then query Creem API to get all information needed to update a subscription.

Querying subscription details
With the subscription ID, call the GET Subscription Creem API.
Retrieve the Item ID from the subscription item.

Retrieve subscription details


Parse the response

Make the subscription change
With the Item Id and Subscription ID, call the UPDATE subscription API.
Use the units field to the desired amount of seats changed.

Update Subscription

​
Manual subscription changes through the Creem Dashboard
You can also modify subscription seats through your Creem Dashboard as a merchant. Simply click on a subscription, and upon the details sheet opening, click on the “Modify Subscription” button


​
Billing after subscription updates
The new amount will be charged upon subscription renewal. If you need pro-rata charges or different use cases, contact the Creem team.

Remember to validate and track seat usage in your application to ensure customers don’t exceed their purchased seat limit.
---
Subscriptions
Introduction
Subscriptions in Creem allow you to create recurring payment plans for your products and services. This documentation will guide you through the key concepts and implementation details of the subscription system.

​
Understanding Subscriptions
A subscription represents a recurring payment agreement between you and your customer. When a customer subscribes to your product, they agree to be billed periodically (monthly, yearly, etc.) until they cancel their subscription.

​
Key Concepts
​
Subscription Status
A subscription can be in different states throughout its lifecycle:

Active: The subscription is current and payments are being processed normally
Past Due: A payment attempt has failed but the subscription is still active and retrying.
Canceled: The subscription has been terminated
Trialing: The subscription is in a trial period before the first payment
Incomplete: The subscription and checkout session was created but not paid
​
Billing Cycles
Subscriptions operate on billing cycles that determine when payments are collected. You can set up various billing intervals:

Monthly billing
Yearly billing
Custom intervals (quarterly, 6 months, etc.)
​
Creating a Subscription
To create a subscription, you’ll need to:

Set up a subscription product in your Creem dashboard
Generate a checkout session for the subscription
Direct your customer to the checkout URL
​
Code Example

Copy
const subscriptionCheckout = await axios.post(
  `https://api.creem.io/v1/checkouts`,
  {
    product_id: 'prod_your-subscription-id',
  },
  {
    headers: { "x-api-key": `creem_123456789` },
  }
);
For more details on checkout session options, navigate to our Checkout Session Introduction

​
Managing Subscriptions
Creem provides several ways to manage active subscriptions:

Update billing information: Customers can update their payment method through the customer portal
Cancel subscriptions: Customers or merchants can cancel at any time, through the Dashboard, customer portal or APIs
Change prices: Merchants can update the price of a subscription, that takes into effect in the next billing cycle
Seat Based Billing: Merchants can update the usage of a specific subscription based on seats or units through the Dashboard or API
---
Subscriptions
Subscription Upgrades
Learn how to manage subscription upgrades and downgrades using Product Bundles in Creem

​
Overview
Subscription upgrades and downgrades in Creem provide a seamless experience for both merchants and customers. When paired with Product Bundles, this functionality becomes even more powerful, enabling self-service subscription management and strategic upsell opportunities.


​
Enhanced Experience with Product Bundles
Product Bundles significantly improve the subscription management experience by:

Self-Service Management: Customers can upgrade or downgrade their subscriptions independently through the Customer Portal
Transparent Tier Comparison: Clear visualization of features and benefits across different subscription tiers
Streamlined Checkout Upsells: Present upgrade options during the initial checkout process to increase conversion to higher tiers
Simplified Product Organization: Group related subscription tiers logically for easier management
By implementing Product Bundles alongside your subscription offerings, you create a more intuitive experience that empowers customers while maximizing your revenue potential through strategic tier positioning.

Product Bundles
Discover how Product Bundles can transform your subscription strategy by organizing offerings into intuitive tiers, boosting conversions, and creating seamless upgrade paths for your customers.

​
Managing Subscription Changes
​
Programmatic Upgrades
To upgrade or downgrade a subscription programmatically, use our subscription upgrade endpoint:


upgrade.sh

upgrade.ts

Copy
curl -X POST https://api.creem.io/v1/subscriptions/sub_123/upgrade \
  -H "x-api-key: creem_123456789" \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": "prod_456",
    "update_behavior": "proration-charge"
  }'
​
update_behavior
string
Controls how the upgrade is processed:

proration-charge-immediately: Charge prorated amount right away. We’ll calculate what your customer has used so far, charge them immediately for the difference, and start a new billing cycle from today.

proration-charge: Add prorated amount to next invoice. We’ll calculate any unused time on the current subscription as credit, apply it to the new subscription price, and bill the difference on the next regular invoice while maintaining the original billing cycle.

proration-none: No proration, change takes effect on next billing cycle. No immediate charges will occur—when the current billing period ends, we’ll simply start charging at the new subscription rate.

​
Best Practices
Upgrade Path

Design clear upgrade paths
Highlight additional value at each tier
Make downgrades equally accessible
Customer Experience

Provide clear feature comparisons
Show pricing differences
Explain pro-rata billing
---
Subscriptions
Product Bundles
Learn how to create and manage Product Bundles to organize your offerings in Creem

​
Overview
Product Bundles are a powerful feature in Creem that allows you to group related products together. Similar to what other platforms call “Variants”, Product Bundles help you organize your offerings in a structured and customer-friendly way.


​
Understanding Product Bundles
​
What are Product Bundles?
Product Bundles are collections of related products that:

Share similar characteristics
Represent different tiers or levels of the same service
Work together as part of a broader offering
Enable streamlined product management
​
Key Benefits
Organized Product Structure
Logical grouping of related products
Clear hierarchy for product offerings
Simplified product management
Enhanced Customer Experience
Easy product comparison
Clear visualization of different tiers
Simplified decision-making process
Flexible Management
Add or remove products from bundles
Adjust pricing and features dynamically
Maintain product relationships
​
Real-World Example
​
Professional Headshots Service Bundle
Here’s how a photography business might structure their offerings using Product Bundles:

1
Basic Package

A starter option priced at $99 that includes:

3 retouched photos
1 outfit change
2
Pro Package

A mid-tier option priced at $199 that includes:

6 retouched photos
2 outfit changes
LinkedIn optimization
3
Enterprise Package

A premium option priced at $499 that includes:

15 retouched photos
Unlimited outfit changes
Full rights to all images
​
Creating and Managing Bundles
​
Create a New Bundle
Creating product bundles in Creem is extremely easy and can be done directly through the dashboard.

​
Creating a Product Bundle
You can create product bundles by navigating to the products page and clicking on the bundles tab, or by navigating directly to https://www.creem.io/dashboard/products/bundles

When creating a new product bundle, you simply need to select which products should be part of it from the dropdown menu. This intuitive interface makes it easy to group related products together and create tiered offerings.

During bundle creation, you’ll find two important toggles that enhance customer experience:

Enable Self-Service Upgrades/Downgrades: When enabled, customers can independently upgrade or downgrade between products within this bundle through the Customer Portal. This empowers customers to manage their subscriptions without requiring merchant intervention.

Enable Upsells at Checkout: When enabled, customers will see upgrade options for higher-tier products within the bundle during checkout. If this toggle is activated, the checkout description will display the product bundle description rather than the individual product description, providing a more cohesive presentation of your tiered offerings.

These options give you flexibility in how customers interact with your product tiers and can significantly improve conversion rates through strategic upsell opportunities.

Create Product Bundle interface showing product selection and upgrade options
Product Bundle creation interface with options for enabling self-service upgrades and checkout upsells

Customer view on Customer Portal when doing an upgrade
Customer Portal view showing available upgrade options within a product bundle

​
Best Practices
​
Bundle Structure
Logical Grouping

Group products that naturally belong together
Maintain clear relationships between products
Use consistent naming conventions across tiers
Value Proposition

Clearly differentiate features between tiers
Maintain logical price progression
Highlight the benefits of each tier
Bundle Organization

Keep bundles focused and specific
Avoid mixing unrelated products
Maintain clear documentation of bundle contents
---
Billing
One Time Payments
One Time Payments in Creem allow you to accept single, non-recurring payments for your products and services. This documentation will guide you through the key concepts and implementation details of the one-time payment system.

​
Understanding One Time Payments
A one time payment represents a single transaction between you and your customer. When a customer makes a one time payment, they are charged once for the full amount of the purchase.

​
Key Concepts
​
Payment Status
A payment can be in different states throughout its lifecycle:

Pending: The payment has been initiated but not yet completed
Paid: The payment has been successfully processed
Refunded: The payment has been refunded to the customer
Partially Refunded: The payment has been partially refunded to the customer
​
Creating a One Time Payment
To create a one time payment, you’ll need to:

Set up a product in your Creem dashboard
Generate a checkout session for the payment
Direct your customer to the checkout URL
​
Code Example

Copy
const paymentCheckout = await axios.post(
  `https://api.creem.io/v1/checkouts`,
  {
    product_id: 'prod_your-product-id',
    request_id: 'your-request-id',
    metadata: {
      customerId: 'your-customer-id'
    }
  },
  {
    headers: { "x-api-key": `creem_123456789` },
  }
);
​
Managing Payments
Creem provides several payment management features:

Refund payments: Process full or partial refunds directly throught the Creem Dashboard
Payment history: View detailed transaction history
Payment metadata: Add custom data to payments for tracking
Payment Custom Fields: Add custom input fields on your checkout session that your users can fill
---
Customers
Introduction
Customers can be queried by your application to refresh statuses and can do actions by themselves using the Customer Portal.

​
Customer Actions
Customers have access to a separated portal where they can manage their subscriptions, payment methods, and personal information. This portal is called the Customer Portal. Get more information about the Customer Portal here.

On the following page, you can also read more about customer receipts, refunds, and how Users access that information.

Customer Portal
Understand how customers can manage their subscriptions and information.

Customer objects can also be queried independently by your application so that you can synchronize specific customer data with their subscription statuses and transaction history.

For example, you can query a customer by their email address to refresh their subscription statuses and update your application’s UI accordingly.


Copy
curl -X 'GET' \
  'http://api.creem.io/v1/customers?email=<EMAIL>' \
  -H 'accept: application/json' \
  -H 'x-api-key: creem_123456789'

Sample Response Body


Copy
  {
      "id": "cust_3biFPNt4Cz5YRDSdIqs7kc",
      "object": "customer",
      "email": "<EMAIL>",
      "name": "Alec Erasmus",
      "country": "SE",
      "created_at": "2024-09-16T16:13:39.265Z",
      "updated_at": "2024-09-16T16:13:39.265Z",
      "mode": "local"
  }
  ---
  Customers
Customer Portal
Customers can cancel and refund subscriptions by themselves.

​
What is a Customer Portal?
After every successful payment, your customers will receive an email with a link to their Customer Portal. This portal allows them to manage their subscriptions, payment methods, and personal information.

This email contains a magic link, to a completely different authentication mechanism, in which they are able to access their account which allows them to execute the aforementioned actions.


Receipt Example


Magic Link Login Example

​
What can customers do in the Customer Portal?
​
1. Cancel a subscription
Upon entering the Customer Portal, customers can cancel their subscriptions by selecting an active subscription, and clicking on the Manage Subscription button. This will open a details sheet on the right side of the screen, where a Cancel Subscription button is available.

This will immediately cancel their subscription and they will no longer be charged for it.


​
2. Request Invoice or Support
Customers using the customer portal, can copy all details of a specific payment, including order_ID and request support from Creem team directly without contacting the merchant.


​
3. Request Customer Portal Login through the API
Merchants can generate a customer portal login for their users, using the API, providing the customer_id. The API responds a URL, that the merchant can then use to redirect their customer to the customer portal.


Response


Copy
{
  "customer_portal_link": "https://creem.io/my-orders/login/xxxxxxxxxx"
}

getCustomerPortalUrl.js

getCustomerPortalUrl.sh

Copy
    const redirectUrl = await axios.post(
      `https://api.creem.io/v1/customers/billing`,
        {
          "customer_id": "cust_xxxxxxx",
        },
        {
          headers: { "x-api-key": `creem_123456789` },
        },
    );
    ---
    Integrations
    ...
