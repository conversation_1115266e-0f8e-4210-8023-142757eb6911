import { Image } from "@/types/image";
import { getSupabaseClient } from "./db";

/**
 * 插入图片记录到 Supabase 数据库
 * @param images 单个 Image 或 Image 数组
 * @returns 插入后的数据
 * @throws 插入失败时抛出错误
 */
export async function insertImage(images: Image | Image[]) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase.from("image").insert(images);

  if (error) {
    console.error("Insert image error:", error);
    throw error;
  }
  return data;
}

/**
 * 分页获取图片记录
 * @param page 页码，默认1
 * @param limit 每页数量，默认50
 * @returns Image 数组
 */
export async function getImage(
  page: number = 1,
  limit: number = 50
): Promise<Image[]> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("image")
    .select("*")
    .order("created_at", { ascending: false })
    .range((page - 1) * limit, page * limit - 1);

  if (error) {
    console.error("Get image error:", error);
    return [];
  }

  return data || [];
}
