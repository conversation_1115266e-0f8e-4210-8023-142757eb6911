@baseUrl = http://localhost:3000
@apiKey = sk-xxx

### ping api
POST {{baseUrl}}/api/ping
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "message": "hello"
}

### gen image
POST {{baseUrl}}/api/demo/gen-image
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "prompt": "a beautiful girl running with 2 cats",
  "provider": "replicate",
  "model": "black-forest-labs/flux-schnell"
}

### gen text 
POST {{baseUrl}}/api/demo/gen-text
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "prompt": "9.11 vs 9.8, which one is greater?",
  "provider": "openrouter",
  "model": "deepseek/deepseek-r1"
}

### gen image
POST {{baseUrl}}/api/gen-image
Content-Type: application/json
Authorization: Bearer {{apiKey}}

{
  "description": "a monkey with a banana",
}

### gen image2
POST http://localhost:3000/api/gen-image
Content-Type: application/json

{
  "description": "a monkey with a banana"
}

### gen image3
POST http://localhost:3000/api/gen-image 
Content-Type: application/json 
Authorization: Bearer ****************************************

{ "description": "a cat with a banana" }