好的，以下是你最终SEO国际化方案的总结文档，适合团队协作和分支迁移。

---

#  项目 SEO 国际化最佳实践总结

## 1. 页面级 SEO 数据存储（推荐）

- **每个页面的 SEO 信息（title/description/keywords）都放在对应的 `i18n/pages/{page}/[locale].json` 文件的 `metadata` 字段下。**
- 例如首页英文SEO配置：  
  `i18n/pages/landing/en.json`
  ```json
  {
    "metadata": {
      "title": "PDF to Quiz Converter: Free AI Quiz Generator & Test Maker",
      "description": "Convert PDF to Quiz instantly with our free AI tool. Transform any PDF document into engaging quizzes & tests. Generate multiple-choice, true/false questions for study, training. Ideal for students, teachers & pros. Secure & fast.",
      "keywords": "pdf to quiz, ai quiz generator, pdf quiz converter, mcq generator, free quiz maker, quiz creator, pdf questions generator, ai assessment tool"
    },
    ...
  }
  ```

## 2. 页面SEO数据读取与使用

- 在页面的 `generateMetadata` 函数中，**直接读取 `page.metadata` 字段**，不再使用全局兜底（`||`）。
- 代码示例（`app/[locale]/(default)/page.tsx`）：
  ```typescript
  const page = await getLandingPage(locale);
  const metadata = page.metadata;

  return {
    title: metadata.title as string,
    description: metadata.description as string,
    keywords: metadata.keywords as string,
    alternates: {
      canonical: canonicalUrl,
    },
  };
  ```
- **注意**：如果你能保证所有页面的 `metadata` 字段都存在且完整，可以直接用 `as string` 或 `!`，否则建议保留兜底。

## 3. layout.tsx 的 SEO 配置
保持原样

## 4. 类型定义

- 在 `types/pages/landing.d.ts` 中为 `LandingPage` 添加可选的 `metadata` 字段，类型如下：
  ```typescript
  export interface PageMetadata {
    title?: string;
    description?: string;
    keywords?: string;
  }

  export interface LandingPage {
    metadata?: PageMetadata;
    // ... 其他字段
  }
  ```

## 5. 其他注意事项

- **每个页面都要有独立的metadata配置**，否则SEO信息会丢失。
- **多语言SEO**：每种语言的SEO信息都要在对应的json文件里维护。
- **迁移/新分支时**，只需复制对应的i18n页面配置和类型定义即可。

---

## 结论

- **页面级SEO国际化配置**，灵活、易维护、最小侵入。
- **不再合并全局title**，每个页面SEO信息独立、精准。
- **类型安全**，如有遗漏可通过TypeScript提示及时发现。

---

如需团队协作或迁移到新分支，直接参考本文档即可快速落地！

