// Creem.io 支付系统类型定义

export interface CreemCheckoutRequest {
  product_id: string;
  request_id?: string;
  units?: number;
  discount_code?: string;
  customer?: {
    id?: string;
    email?: string;
  };
  custom_field?: Array<{
    type: string;
    key: string;
    label: string;
    optional: boolean;
    text?: {
      max_length: number;
      min_length: number;
    };
  }>;
  success_url?: string;
  metadata?: Record<string, any>;
}

export interface CreemCheckoutResponse {
  id: string;
  mode: 'test' | 'live' | 'sandbox';
  object: string;
  status: string;
  product: CreemProduct;
  checkout_url: string;
  success_url: string;
  request_id?: string;
  units?: number;
  order?: CreemOrder;
  subscription?: CreemSubscription;
  customer?: CreemCustomer;
  custom_fields?: Array<any>;
  feature?: Array<any>;
  metadata?: Record<string, any>;
}

export interface CreemProduct {
  id: string;
  mode: 'test' | 'live' | 'sandbox';
  object: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  billing_type: 'recurring' | 'one-time';
  billing_period?: string;
  status: string;
  tax_mode: 'inclusive' | 'exclusive';
  tax_category: string;
  product_url: string;
  default_success_url?: string;
  image_url?: string;
  features?: Array<{
    id: string;
    type: string;
    description: string;
  }>;
  created_at: string;
  updated_at: string;
}

export interface CreemOrder {
  id: string;
  mode: 'test' | 'live' | 'sandbox';
  object: string;
  customer: string | CreemCustomer;
  product: string | CreemProduct;
  amount: number;
  currency: string;
  fx_amount?: number;
  fx_currency?: string;
  fx_rate?: number;
  status: 'pending' | 'paid' | 'refunded' | 'partially_refunded';
  type: 'subscription' | 'one-time';
  affiliate?: string;
  created_at: string;
  updated_at: string;
}

export interface CreemCustomer {
  id: string;
  mode: 'test' | 'live' | 'sandbox';
  object: string;
  email: string;
  name?: string;
  country: string;
  created_at: string;
  updated_at: string;
}

export interface CreemSubscription {
  id: string;
  mode: 'test' | 'live' | 'sandbox';
  object: string;
  product: string | CreemProduct;
  customer: string | CreemCustomer;
  items?: Array<{
    id: string;
    mode: 'test' | 'live' | 'sandbox';
    object: string;
    product_id: string;
    price_id: string;
    units: number;
  }>;
  collection_method: string;
  status: 'active' | 'canceled' | 'unpaid' | 'paused' | 'trialing';
  last_transaction_id?: string;
  last_transaction_date?: string;
  next_transaction_date?: string;
  current_period_start_date?: string;
  current_period_end_date?: string;
  canceled_at?: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

// Webhook 事件类型
export interface CreemWebhookEvent {
  id: string;
  eventType: 'checkout.completed' | 'subscription.active' | 'subscription.paid' | 'subscription.canceled' | 'subscription.expired' | 'refund.created' | 'subscription.update' | 'subscription.trialing';
  created_at: number;
  object: CreemCheckoutResponse | CreemSubscription | any;
}

// API 错误响应
export interface CreemErrorResponse {
  error?: {
    message?: string;
    [key: string]: any;
  };
  message?: string;
  [key: string]: any;
}

// 客户门户响应
export interface CreemCustomerPortalResponse {
  customer_portal_link: string;
}

// 产品创建请求
export interface CreemProductCreateRequest {
  name: string;
  price: number;
  currency: string;
  billing_type: 'recurring' | 'one-time';
  description?: string;
  image_url?: string;
  billing_period?: string;
  features?: Array<{
    id: string;
    type: string;
    description: string;
  }>;
  tax_mode?: 'inclusive' | 'exclusive';
  tax_category?: string;
  default_success_url?: string;
  custom_field?: Array<{
    type: string;
    key: string;
    label: string;
    optional: boolean;
    text?: {
      max_length: number;
      min_length: number;
    };
  }>;
}

// 配置接口
export interface CreemConfig {
  apiKey: string;
  apiUrl: string;
  mode: 'test' | 'live';
  webhookSecret?: string;
}
