import { Badge } from "@/components/ui/badge";
import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";

export default function Feature2Server({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-32">
      <div className="container">
        <div className="text-center mb-16">
          {section.label && (
            <Badge variant="outline" className="mb-4">
              {section.label}
            </Badge>
          )}
          <h2 className="mb-6 text-pretty text-3xl font-bold lg:text-4xl">
            {section.title}
          </h2>
          <p className="mb-4 max-w-xl mx-auto text-muted-foreground lg:max-w-none lg:text-lg">
            {section.description}
          </p>
        </div>
        
        {/* 每个item都有图片和文字 */}
        <div className="space-y-12">
          {section.items?.map((item, i) => (
            <div key={i} className={`grid gap-8 lg:grid-cols-2 items-center ${i % 2 === 1 ? 'lg:grid-cols-2' : ''}`}>
              {/* 奇数项：图片在右，偶数项：图片在左 */}
              <div className={`${i % 2 === 1 ? 'lg:order-2' : ''}`}>
                <div className="flex items-center gap-4 mb-4">
                  {item.icon && (
                    <div className="flex size-12 items-center justify-center rounded-lg bg-primary/10">
                      <Icon
                        name={item.icon}
                        className="size-6 text-primary"
                      />
                    </div>
                  )}
                  <h3 className="text-xl font-semibold">{item.title}</h3>
                </div>
                <p className="text-muted-foreground lg:text-base leading-relaxed">
                  {item.description}
                </p>
              </div>
              
              <div className={`${i % 2 === 1 ? 'lg:order-1' : ''}`}>
                {item.image && (
                  <img
                    src={item.image.src}
                    alt={item.image.alt || item.title}
                    className="w-full h-64 object-cover rounded-lg shadow-md"
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
} 