import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
// 移除 Feature2
// import Feature2 from "@/components/blocks/feature2";
import Feature2Server from "@/components/blocks/feature2-server";
import Feature3 from "@/components/blocks/feature3";
// 暂时使用普通的Hero组件而不是HeroWithUpload
import Hero from "@/components/blocks/hero";
// import HeroWithUpload from "@/components/blocks/hero/HeroWithUpload";
import Generator from "@/components/generator";
import Pricing from "@/components/blocks/pricing";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import TestimonialServer from "@/components/blocks/testimonial-server";
import { getLandingPage } from "@/services/page";
// 暂时注释掉图片相关的导入
// import GhibliImageGallery from "@/components/ghibli-image-gallery";
// import { getImage } from "@/models/image";
// import { GhibliImage } from "@/components/ghibli-image-gallery";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);
  const metadata = page.metadata;
  
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    title: metadata?.title as string,
    description: metadata?.description as string,
    keywords: metadata?.keywords as string,
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const page = await getLandingPage(locale);
  
  // 暂时注释掉可能出错的getImage调用
  // const initialImages = await getImage(1, 6);
  
  // 暂时注释掉图片处理逻辑
  // const ghibliImages: GhibliImage[] = initialImages.map(image => ({
  //   id: image.uuid,
  //   title: image.img_description.split(",")[0].replace("A high-resolution Studio Ghibli-style illustration of ", ""),
  //   img_url: image.img_url,
  //   dimensions: "1792×1024",
  //   author: {
  //     name: "User",
  //   },
  // }));
  
  return (
    <>
      {/* 使用普通Hero组件而不是HeroWithUpload */}
      {page.hero && <Hero hero={page.hero} />}
      {/* {page.hero && <HeroWithUpload hero={page.hero} />} */}
      
      <Generator />
      {/* <GhibliImageGallery images={ghibliImages} /> */}
      {/* {page.branding && <Branding section={page.branding} />} */}
      
      {page.introduce && <Feature1 section={page.introduce} />}
      
      {/* 替换Feature2为Feature1 */}
      {page.benefit && <Feature2Server section={page.benefit} />}
      
      {page.usage && <Feature3 section={page.usage} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.testimonial && <TestimonialServer section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
