//改成完全服务端渲染，SEO 友好，图片和描述会直接输出到 HTML

import { Badge } from "@/components/ui/badge";
import { Section as SectionType } from "@/types/blocks/section";

export default function Showcase({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      <div className="container">
        <div className="mb-8 flex items-end justify-between md:mb-14 lg:mb-16">
          <h2 className="mb-2 text-pretty text-3xl font-bold lg:text-4xl">
            {section.title}
          </h2>
        </div>
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {section.items?.map((item, i) => (
            <div
              key={i}
              className="group flex flex-col justify-between rounded-xl border border-border bg-card p-6"
            >
              <div>
                <div className="flex aspect-[3/2] overflow-clip rounded-xl">
                  <div className="flex-1">
                    <div className="relative h-full w-full origin-bottom transition duration-300 group-hover:scale-105">
                      <img
                        src={item.image?.src}
                        alt={item.image?.alt || item.title}
                        className="h-full w-full object-cover object-center"
                      />
                    </div>
                  </div>
                </div>
              </div>
              {item.label && (
                <div className="mt-6">
                  <Badge>{item.label}</Badge>
                </div>
              )}
              <div className="mb-2 break-words pt-4 text-lg font-medium md:mb-3 md:pt-4 md:text-xl lg:pt-4 lg:text-2xl">
                {item.title}
              </div>
              <div className="mb-2 text-sm text-muted-foreground md:mb-2 md:text-base lg:mb-2">
                {item.description}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
