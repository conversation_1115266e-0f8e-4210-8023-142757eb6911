import { CreemService } from "@/services/creem";
import { handleCreemOrderSession } from "@/services/order";
import { redirect } from "next/navigation";

export default async function CreemSuccessPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  try {
    const params = await searchParams;
    
    // 从 URL 参数中获取 Creem 返回的信息
    const {
      checkout_id,
      order_id,
      customer_id,
      subscription_id,
      product_id,
      request_id,
      signature
    } = params;

    console.log("Creem success page params:", params);

    if (!checkout_id) {
      console.error("Missing checkout_id in success URL");
      redirect(process.env.NEXT_PUBLIC_PAY_FAIL_URL || "/");
      return;
    }

    // 验证签名（根据 Creem 官方文档）
    if (signature && process.env.CREEM_API_KEY) {
      console.log("Verifying Creem signature...");
      
      // 按 Creem 文档要求构建签名验证数据
      const signatureParams = Object.entries(params)
        .filter(([key]) => key !== 'signature')
        .sort(([a], [b]) => a.localeCompare(b)) // 按键名排序
        .map(([key, value]) => `${key}=${value}`)
        .concat(`salt=${process.env.CREEM_API_KEY}`)
        .join('|');
      
      console.log("Signature verification data:", signatureParams);
      
      const crypto = require('crypto');
      const expectedSignature = crypto.createHash('sha256').update(signatureParams).digest('hex');
      
      console.log("Expected signature:", expectedSignature);
      console.log("Received signature:", signature);
      
      if (expectedSignature !== signature) {
        console.error("Invalid signature in success URL");
        redirect(process.env.NEXT_PUBLIC_PAY_FAIL_URL || "/");
        return;
      }
      
      console.log("Signature verification successful");
    } else {
      console.warn("Skipping signature verification (no signature or API key)");
    }

    // 获取支付会话详情
    const creemService = CreemService.getInstance();
    const checkoutSession = await creemService.getCheckoutSession(checkout_id as string);

    // 处理订单会话（更新订单状态、增加积分等）
    await handleCreemOrderSession(checkoutSession);

    console.log("Creem payment processed successfully:", {
      checkout_id,
      order_id,
      request_id
    });

    // 重定向到成功页面
    redirect(process.env.NEXT_PUBLIC_PAY_SUCCESS_URL || "/");

  } catch (e: any) {
    console.error("Creem success page error:", e);
    redirect(process.env.NEXT_PUBLIC_PAY_FAIL_URL || "/");
  }
}
