# FAQ Accordion Component

## 概述

FAQ组件已升级为手风琴式可折叠组件，提供更好的用户体验和SEO优化。

## 功能特性

### ✅ 已实现的功能需求
- **手风琴式展开/折叠交互**：平滑的动画效果
- **独立控制**：每个FAQ项目可以独立展开或收起
- **多种模式**：支持同时展开多个项目或只允许展开一个项目
- **默认状态配置**：可配置默认展开的项目

### ✅ SEO优化特性
- **服务端渲染兼容**：所有内容在初始HTML中完整呈现
- **爬虫友好**：搜索引擎可以直接读取所有问题和答案
- **无JavaScript依赖**：即使JavaScript禁用也能显示完整内容
- **语义化HTML**：使用Radix UI的语义化结构

### ✅ 用户体验优化
- **平滑动画**：使用Tailwind CSS的accordion动画
- **响应式设计**：在移动端和桌面端都有良好体验
- **键盘导航**：完整的键盘导航支持（Tab、Enter、Space）
- **无障碍访问**：完整的ARIA属性支持

## 使用方法

### 基础使用（向后兼容）
```tsx
import FAQ from "@/components/blocks/faq";

// 现有代码无需修改，默认为multiple模式
<FAQ section={faqSection} />
```

### 高级配置
```tsx
import FAQ from "@/components/blocks/faq";

// 单选模式（一次只能展开一个）
<FAQ 
  section={faqSection} 
  accordionType="single"
  defaultOpen={0}  // 默认展开第一个
/>

// 多选模式（可同时展开多个）
<FAQ 
  section={faqSection} 
  accordionType="multiple"
  defaultOpen={[0, 2]}  // 默认展开第1和第3个
  showNumbers={true}    // 显示编号（默认）
/>

// 隐藏编号的简洁模式
<FAQ 
  section={faqSection} 
  showNumbers={false}
/>
```

## Props API

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `section` | `SectionType` | 必需 | FAQ数据，包含items数组 |
| `accordionType` | `"single" \| "multiple"` | `"multiple"` | 手风琴模式 |
| `defaultOpen` | `number \| number[]` | `undefined` | 默认展开的项目索引 |
| `showNumbers` | `boolean` | `true` | 是否显示编号 |

## 数据结构

FAQ组件使用标准的Section类型，无需修改现有数据结构：

```typescript
interface Section {
  name?: string;
  title?: string;
  description?: string;
  label?: string;
  items?: SectionItem[];
}

interface SectionItem {
  title?: string;        // FAQ问题
  description?: string;  // FAQ答案
}
```

## 样式特性

### 视觉设计
- **卡片式布局**：每个FAQ项目使用独立的卡片
- **圆角设计**：现代化的圆角边框
- **阴影效果**：悬停时的阴影变化
- **编号指示器**：圆形编号，展开时变色

### 交互反馈
- **悬停效果**：鼠标悬停时的视觉反馈
- **焦点状态**：键盘导航时的焦点环
- **状态变化**：展开/收起时的平滑动画
- **颜色变化**：编号在展开时变为主色调

### 响应式适配
- **移动端优化**：在小屏幕上的良好显示
- **触摸友好**：适合触摸操作的按钮大小
- **文本可读性**：合适的行高和间距

## 无障碍访问

### ARIA支持
- **aria-label**：为每个触发器提供描述性标签
- **role属性**：正确的语义角色
- **状态指示**：展开/收起状态的正确传达

### 键盘导航
- **Tab键**：在FAQ项目间导航
- **Enter/Space**：展开/收起当前项目
- **焦点管理**：正确的焦点顺序和可见性

## 技术实现

### 核心依赖
- **Radix UI Accordion**：提供无障碍的手风琴功能
- **Tailwind CSS**：样式和动画
- **TypeScript**：类型安全

### SSR兼容性
- **服务端渲染**：完全支持Next.js SSR
- **水合安全**：避免客户端/服务端不一致
- **SEO友好**：搜索引擎可索引所有内容

## 迁移指南

### 从旧版本升级
现有代码无需修改，新组件完全向后兼容：

```tsx
// 旧代码 - 继续工作
<FAQ section={faqSection} />

// 新功能 - 可选升级
<FAQ 
  section={faqSection} 
  accordionType="single"
  defaultOpen={0}
/>
```

### 性能优化
- **代码分割**：组件支持懒加载
- **动画性能**：使用CSS动画而非JavaScript
- **内存效率**：最小的JavaScript占用

## 最佳实践

### 内容组织
- **问题简洁**：FAQ标题应简洁明了
- **答案详细**：提供完整的解答信息
- **逻辑排序**：按重要性或相关性排序

### 用户体验
- **默认状态**：考虑是否需要默认展开重要问题
- **数量控制**：避免FAQ项目过多，影响页面性能
- **内容质量**：确保答案准确且有帮助

### SEO优化
- **关键词优化**：在问题和答案中自然包含关键词
- **结构化数据**：考虑添加FAQ结构化数据
- **内容更新**：定期更新FAQ内容保持相关性
