import { Avatar, AvatarImage } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";
import { Star } from "lucide-react";

export default function TestimonialServer({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  return (
    <section id={section.name} className="py-16">
      <div className="container">
        <div className="flex flex-col items-center gap-4 mb-16">
          {section.label && (
            <div className="flex items-center gap-1 text-sm font-semibold text-primary">
              {section.icon && (
                <Icon name={section.icon} className="h-6 w-auto border-primary" />
              )}
              {section.label}
            </div>
          )}
          <h2 className="text-center text-3xl font-semibold lg:text-4xl">
            {section.title}
          </h2>
          <p className="text-center text-muted-foreground lg:text-lg">
            {section.description}
          </p>
        </div>
        
        {/* 网格布局展示所有testimonials */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {section.items?.map((item, index) => (
            <Card key={index} className="p-6">
              <div className="flex justify-between mb-4">
                <div className="flex gap-4">
                  <Avatar className="size-14 rounded-full ring-1 ring-input">
                    <AvatarImage
                      src={item.image?.src}
                      alt={item.image?.alt || item.title}
                    />
                  </Avatar>
                  <div>
                    <p className="font-medium">{item.title}</p>
                    <p className="text-sm text-muted-foreground">
                      {item.label}
                    </p>
                  </div>
                </div>
                <div className="flex gap-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className="size-5 fill-amber-500 text-amber-500"
                    />
                  ))}
                </div>
              </div>
              <q className="leading-7 text-muted-foreground">
                {item.description}
              </q>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
} 