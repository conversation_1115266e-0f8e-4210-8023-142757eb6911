// PDF处理相关类型定义

export interface PDFProcessingOptions {
  extractImages: boolean;
  preserveFormatting: boolean;
  language?: string;
  ocrEnabled: boolean;
  pageRange?: {
    start: number;
    end: number;
  };
}

export interface PDFTextContent {
  text: string;
  pages: PDFPageContent[];
  metadata: PDFMetadata;
  processingTime: number;
  wordCount: number;
  characterCount: number;
}

export interface PDFPageContent {
  pageNumber: number;
  text: string;
  wordCount: number;
  hasImages: boolean;
  confidence?: number; // OCR置信度
}

export interface PDFMetadata {
  title?: string;
  author?: string;
  subject?: string;
  creator?: string;
  producer?: string;
  creationDate?: Date;
  modificationDate?: Date;
  pageCount: number;
  fileSize: number;
  version?: string;
  isEncrypted: boolean;
  hasFormFields: boolean;
}

export interface PDFValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  fileInfo: {
    name: string;
    size: number;
    type: string;
    lastModified: Date;
  };
  canProcess: boolean;
  estimatedProcessingTime?: number;
}

export interface PDFProcessingResult {
  success: boolean;
  content?: PDFTextContent;
  error?: string;
  processingTime: number;
  extractedImages?: string[];
  quality: 'high' | 'medium' | 'low';
}

export interface SupportedFileType {
  extension: string;
  mimeType: string;
  maxSize: number; // bytes
  processor: 'pdf-parse' | 'docx-parser' | 'text-reader' | 'image-ocr';
  description: string;
}

export interface FileUploadResult {
  success: boolean;
  fileId?: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  uploadedAt: Date;
  error?: string;
  validationResult: PDFValidationResult;
}

export interface OCRResult {
  text: string;
  confidence: number;
  language: string;
  processingTime: number;
  boundingBoxes?: {
    text: string;
    x: number;
    y: number;
    width: number;
    height: number;
    confidence: number;
  }[];
}

export interface DocumentSection {
  title?: string;
  content: string;
  pageNumbers: number[];
  type: 'heading' | 'paragraph' | 'list' | 'table' | 'image' | 'other';
  level?: number; // 标题级别
  wordCount: number;
}
