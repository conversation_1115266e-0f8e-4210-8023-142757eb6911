import {
  CreemCheckoutRequest,
  CreemCheckoutResponse,
  CreemErrorResponse,
  CreemConfig,
  CreemProduct,
  CreemCustomer,
  CreemSubscription
} from '@/types/creem';

/**
 * Creem.io 支付服务类
 * 提供与 Creem.io API 交互的方法
 */
export class CreemService {
  private config: CreemConfig;

  constructor(config: CreemConfig) {
    this.config = config;
  }

  /**
   * 创建静态实例
   */
  public static getInstance(): CreemService {
    const apiKey = process.env.CREEM_API_KEY;
    const apiUrl = process.env.CREEM_API_URL || 'https://api.creem.io';
    const mode = (process.env.CREEM_MODE as 'test' | 'live') || 'test';
    const webhookSecret = process.env.CREEM_WEBHOOK_SECRET;

    if (!apiKey) {
      throw new Error('CREEM_API_KEY environment variable is required');
    }

    return new CreemService({
      apiKey,
      apiUrl,
      mode,
      webhookSecret
    });
  }

  /**
   * 创建支付会话
   */
  public async createCheckoutSession(params: CreemCheckoutRequest): Promise<CreemCheckoutResponse> {
    try {
      console.log('Creating Creem checkout session with params:', JSON.stringify(params));

      const response = await fetch(`${this.config.apiUrl}/v1/checkouts`, {
        method: 'POST',
        headers: {
          'x-api-key': this.config.apiKey,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(params)
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('Creem API Error creating checkout:', { status: response.status, body: responseData });
        const typedResponseData = responseData as CreemErrorResponse;
        const errorMessage = typedResponseData?.error?.message || typedResponseData?.message || `Failed to create checkout session (HTTP ${response.status})`;
        throw new Error(errorMessage);
      }

      const successfulResponseData = responseData as CreemCheckoutResponse;
      if (!successfulResponseData.checkout_url) {
        console.error('Creem API response missing checkout URL:', responseData);
        throw new Error('Creem checkout session created but checkout_url is missing.');
      }

      console.log('Creem checkout session created successfully. URL:', successfulResponseData.checkout_url);
      return successfulResponseData;

    } catch (error) {
      console.error('Error creating Creem checkout session:', error);
      throw error;
    }
  }

  /**
   * 获取支付会话信息
   */
  public async getCheckoutSession(checkoutId: string): Promise<CreemCheckoutResponse> {
    try {
      const response = await fetch(`${this.config.apiUrl}/v1/checkouts?checkout_id=${checkoutId}`, {
        method: 'GET',
        headers: {
          'x-api-key': this.config.apiKey,
          'Accept': 'application/json'
        }
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('Creem API Error getting checkout:', { status: response.status, body: responseData });
        const typedResponseData = responseData as CreemErrorResponse;
        const errorMessage = typedResponseData?.error?.message || typedResponseData?.message || `Failed to get checkout session (HTTP ${response.status})`;
        throw new Error(errorMessage);
      }

      return responseData as CreemCheckoutResponse;

    } catch (error) {
      console.error('Error getting Creem checkout session:', error);
      throw error;
    }
  }

  /**
   * 获取客户信息
   */
  public async getCustomer(customerIdOrEmail: string): Promise<CreemCustomer> {
    try {
      const isEmail = customerIdOrEmail.includes('@');
      const queryParam = isEmail ? `email=${encodeURIComponent(customerIdOrEmail)}` : `customer_id=${customerIdOrEmail}`;

      const response = await fetch(`${this.config.apiUrl}/v1/customers?${queryParam}`, {
        method: 'GET',
        headers: {
          'x-api-key': this.config.apiKey,
          'Accept': 'application/json'
        }
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('Creem API Error getting customer:', { status: response.status, body: responseData });
        const typedResponseData = responseData as CreemErrorResponse;
        const errorMessage = typedResponseData?.error?.message || typedResponseData?.message || `Failed to get customer (HTTP ${response.status})`;
        throw new Error(errorMessage);
      }

      return responseData as CreemCustomer;

    } catch (error) {
      console.error('Error getting Creem customer:', error);
      throw error;
    }
  }

  /**
   * 获取订阅信息
   */
  public async getSubscription(subscriptionId: string): Promise<CreemSubscription> {
    try {
      const response = await fetch(`${this.config.apiUrl}/v1/subscriptions?subscription_id=${subscriptionId}`, {
        method: 'GET',
        headers: {
          'x-api-key': this.config.apiKey,
          'Accept': 'application/json'
        }
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('Creem API Error getting subscription:', { status: response.status, body: responseData });
        const typedResponseData = responseData as CreemErrorResponse;
        const errorMessage = typedResponseData?.error?.message || typedResponseData?.message || `Failed to get subscription (HTTP ${response.status})`;
        throw new Error(errorMessage);
      }

      return responseData as CreemSubscription;

    } catch (error) {
      console.error('Error getting Creem subscription:', error);
      throw error;
    }
  }

  // 客户门户功能已移除 - 项目不需要复杂的订阅管理
  // 用户可以通过 my-orders 和 my-credits 页面管理他们的购买和积分

  /**
   * 验证 webhook 签名（HMAC-SHA256，用于 Creem Webhook）
   */
  public verifyWebhookSignature(payload: string, signature: string): boolean {
    if (!this.config.webhookSecret) {
      console.warn('Webhook secret not configured, skipping signature verification');
      return true; // 如果没有配置密钥，跳过验证
    }

    try {
      const crypto = require('crypto');
      const computedSignature = crypto
        .createHmac('sha256', this.config.webhookSecret)
        .update(payload)
        .digest('hex');

      return computedSignature === signature;
    } catch (error) {
      console.error('Error verifying webhook signature:', error);
      return false;
    }
  }

  /**
   * 验证 Creem Return URL 签名
   * @param params 返回 URL 上的查询参数对象
   * @param signature Creem 返回的签名字符串
   * @returns 签名是否有效
   */
  public verifyReturnUrlSignature(
    params: Record<string, string | string[] | undefined>,
    signature: string
  ): boolean {
    try {
      const crypto = require('crypto');
      // 按键名排序，忽略 signature 字段，并拼接 salt
      const signatureData = Object.entries(params)
        .filter(([key, value]) => key !== 'signature' && value != null)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([key, value]) => {
          // 如果是数组，取第一个元素
          const v = Array.isArray(value) ? value[0] : value;
          return `${key}=${v}`;
        })
        .concat(`salt=${this.config.apiKey}`)
        .join('|');

      const computed = crypto
        .createHash('sha256')
        .update(signatureData)
        .digest('hex');

      return computed === signature;
    } catch (error) {
      console.error('Error verifying return URL signature:', error);
      return false;
    }
  }

  /**
   * 获取产品信息
   */
  public async getProduct(productId: string): Promise<CreemProduct> {
    try {
      const response = await fetch(`${this.config.apiUrl}/v1/products?product_id=${productId}`, {
        method: 'GET',
        headers: {
          'x-api-key': this.config.apiKey,
          'Accept': 'application/json'
        }
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('Creem API Error getting product:', { status: response.status, body: responseData });
        const typedResponseData = responseData as CreemErrorResponse;
        const errorMessage = typedResponseData?.error?.message || typedResponseData?.message || `Failed to get product (HTTP ${response.status})`;
        throw new Error(errorMessage);
      }

      return responseData as CreemProduct;

    } catch (error) {
      console.error('Error getting Creem product:', error);
      throw error;
    }
  }
}
