import {
  CreditsTransType,
  increaseCredits,
  updateCreditForOrder,
} from "./credit";
import { findOrderByOrderNo, updateOrderStatus } from "@/models/order";
import { getIsoTimestr, getOneYearLaterTimestr } from "@/lib/time";

import <PERSON><PERSON> from "stripe";
import { updateAffiliateForOrder } from "./affiliate";
import { CreemCheckoutResponse } from "@/types/creem";

export async function handleOrderSession(session: Stripe.Checkout.Session) {
  try {
    if (
      !session ||
      !session.metadata ||
      !session.metadata.order_no ||
      session.payment_status !== "paid"
    ) {
      throw new Error("invalid session");
    }

    const order_no = session.metadata.order_no;
    const paid_email =
      session.customer_details?.email || session.customer_email || "";
    const paid_detail = JSON.stringify(session);

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== "created") {
      throw new Error("invalid order");
    }

    const paid_at = getIsoTimestr();
    await updateOrderStatus(order_no, "paid", paid_at, paid_email, paid_detail);

    if (order.user_uuid) {
      if (order.credits > 0) {
        // increase credits for paied order
        await updateCreditForOrder(order);
      }

      // update affiliate for paied order
      await updateAffiliateForOrder(order);
    }

    console.log(
      "handle order session successed: ",
      order_no,
      paid_at,
      paid_email,
      paid_detail
    );
  } catch (e) {
    console.log("handle order session failed: ", e);
    throw e;
  }
}

export async function handleCreemOrderSession(checkoutSession: CreemCheckoutResponse) {
  try {
    const metadata = checkoutSession.metadata;

    if (!metadata) {
      throw new Error("Creem checkout session missing metadata");
    }

    const order_no = metadata.order_no;

    if (!order_no) {
      throw new Error("order_no is required in Creem metadata");
    }

    // 检查订单状态，避免重复处理
    if (checkoutSession.status !== 'completed' && checkoutSession.order?.status !== 'paid') {
      console.log("Creem checkout not completed yet, skipping processing", {
        checkout_status: checkoutSession.status,
        order_status: checkoutSession.order?.status
      });
      return;
    }

    const paid_email = checkoutSession.customer?.email || "";
    const paid_detail = JSON.stringify(checkoutSession);

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== "created") {
      throw new Error("invalid order");
    }

    const paid_at = getIsoTimestr();
    await updateOrderStatus(order_no, "paid", paid_at, paid_email, paid_detail);

    if (order.user_uuid) {
      if (order.credits > 0) {
        // increase credits for paid order
        await updateCreditForOrder(order);
      }

      // update affiliate for paid order
      await updateAffiliateForOrder(order);
    }

    console.log(
      "handle Creem order session succeeded: ",
      order_no,
      paid_at,
      paid_email,
      checkoutSession.id
    );
  } catch (e) {
    console.log("handle Creem order session failed: ", e);
    throw e;
  }
}
