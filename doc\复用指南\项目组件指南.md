# 项目组件指南

## 目录

- [项目组件架构概览](#项目组件架构概览)
- [组件清单](#组件清单)
- [类型系统](#类型系统)
- [数据模型](#数据模型)
- [新增组件指南](#新增组件指南)
- [最佳实践](#最佳实践)

## 项目组件架构概览

本项目采用 Next.js + TypeScript + Tailwind CSS 构建，组件架构遵循以下原则：
- **类型驱动开发**：所有组件都有对应的 TypeScript 类型定义
- **模块化设计**：组件按功能和用途分类存放
- **复用性优先**：基础 UI 组件可被业务组件复用

### 目录结构

```
components/
├── analytics/          # 分析追踪组件
├── blocks/            # 业务块组件
├── console/           # 控制台相关组件
├── dashboard/         # 仪表板组件
├── generator/         # 生成器组件
├── sign/              # 登录注册组件
├── ui/                # 基础 UI 组件
└── upload/            # 上传组件
```

## 组件清单

### 1. 基础 UI 组件 (`components/ui/`)

这些是项目的原子级组件，基于 shadcn/ui 构建：

| 组件名 | 文件名 | 描述 |
|--------|--------|------|
| Accordion | `accordion.tsx` | 折叠面板 |
| Alert | `alert.tsx` | 警告提示 |
| Avatar | `avatar.tsx` | 头像 |
| Badge | `badge.tsx` | 徽章标签 |
| Button | `button.tsx` | 按钮（支持多种变体） |
| Card | `card.tsx` | 卡片容器 |
| Dialog | `dialog.tsx` | 对话框 |
| Drawer | `drawer.tsx` | 抽屉 |
| Form | `form.tsx` | 表单组件 |
| Input | `input.tsx` | 输入框 |
| Label | `label.tsx` | 标签 |
| Select | `select.tsx` | 选择器 |
| Table | `table.tsx` | 表格 |
| Tabs | `tabs.tsx` | 标签页 |
| Tooltip | `tooltip.tsx` | 工具提示 |

### 2. 业务块组件 (`components/blocks/`)

这些是由基础组件组合而成的业务组件：

| 组件名 | 目录 | 描述 |
|--------|------|------|
| Hero | `hero/` | 首页大图区域 |
| Blog | `blog/` | 博客列表 |
| BlogDetail | `blog-detail/` | 博客详情 |
| Pricing | `pricing/` | 价格表 |
| FAQ | `faq/` | 常见问题 |
| Feature | `feature/` | 特性展示 |
| Footer | `footer/` | 页脚 |
| Header | `header/` | 页头导航 |
| Stats | `stats/` | 统计数据展示 |
| Testimonial | `testimonial-server/` | 用户评价 |
| CTA | `cta/` | 行动号召 |
| Form | `form/` | 业务表单 |
| Table | `table/` | 业务表格 |

### 3. 仪表板组件 (`components/dashboard/`)

用于管理后台的组件：

| 组件名 | 文件/目录 | 描述 |
|--------|-----------|------|
| Layout | `layout.tsx` | 仪表板布局 |
| Header | `header/` | 仪表板头部 |
| Sidebar | `sidebar/` | 侧边栏导航 |
| Form Slot | `slots/form/` | 表单插槽 |
| Table Slot | `slots/table/` | 表格插槽 |

### 4. 特殊功能组件

| 组件名 | 路径 | 描述 |
|--------|------|------|
| SignIn | `sign/sign_in.tsx` | 登录组件 |
| LocaleToggle | `locale/toggle.tsx` | 语言切换 |
| ThemeToggle | `theme/toggle.tsx` | 主题切换 |

## 类型系统

### 1. 基础类型定义 (`types/blocks/base.d.ts`)

```typescript
// 按钮类型
export interface Button {
  title?: string;
  icon?: string;
  url?: string;
  target?: string;
  type?: ButtonType;
  variant?: ButtonVariant;
  size?: ButtonSize;
  className?: string;
}

// 图片类型
export interface Image {
  src?: string;
  alt?: string;
  className?: string;
}

// 导航项类型
export interface NavItem {
  name?: string;
  title?: string;
  description?: string;
  icon?: string;
  image?: Image;
  url?: string;
  target?: string;
  is_active?: boolean;
  is_expand?: boolean;
  className?: string;
  children?: NavItem[];
}
```

### 2. 业务组件类型

每个业务组件都有对应的类型定义文件：

- `types/blocks/hero.d.ts` - Hero 组件类型
- `types/blocks/pricing.d.ts` - 价格组件类型
- `types/blocks/form.d.ts` - 表单组件类型
- `types/blocks/table.d.ts` - 表格组件类型

### 3. 数据模型类型

- `types/user.d.ts` - 用户数据类型
- `types/order.d.ts` - 订单数据类型
- `types/quiz.d.ts` - 测验数据类型
- `types/pdf.d.ts` - PDF 相关类型

## 数据模型

### 模型文件 (`models/`)

| 模型 | 文件 | 功能描述 |
|------|------|----------|
| User | `user.ts` | 用户的增删改查操作 |
| Order | `order.ts` | 订单管理 |
| Post | `post.ts` | 博客文章管理 |
| Credit | `credit.ts` | 积分管理 |
| ApiKey | `apikey.ts` | API 密钥管理 |
| Image | `image.ts` | 图片管理 |

## 新增组件指南

### 1. 新增基础 UI 组件

以新增一个 `Notification` 组件为例：

**步骤 1**: 创建组件文件
```bash
# 在 components/ui/ 目录下创建
components/ui/notification.tsx
```

**步骤 2**: 编写组件代码
```typescript
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const notificationVariants = cva(
  "relative w-full rounded-lg border p-4",
  {
    variants: {
      variant: {
        default: "bg-background text-foreground",
        success: "border-green-200 bg-green-50 text-green-900",
        warning: "border-yellow-200 bg-yellow-50 text-yellow-900",
        error: "border-red-200 bg-red-50 text-red-900",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface NotificationProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof notificationVariants> {
  title?: string
  description?: string
}

const Notification = React.forwardRef<HTMLDivElement, NotificationProps>(
  ({ className, variant, title, description, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(notificationVariants({ variant }), className)}
        {...props}
      >
        {title && <h4 className="font-semibold">{title}</h4>}
        {description && <p className="text-sm mt-1">{description}</p>}
      </div>
    )
  }
)
Notification.displayName = "Notification"

export { Notification, notificationVariants }
```

### 2. 新增业务组件

以新增一个 `QuizResult` 组件为例：

**步骤 1**: 创建类型定义
```bash
# 在 types/blocks/ 目录下创建
types/blocks/quiz-result.d.ts
```

```typescript
export interface QuizResult {
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent?: number;
  feedback?: string;
  showDetails?: boolean;
}
```

**步骤 2**: 创建组件目录和文件
```bash
# 创建组件目录
components/blocks/quiz-result/
└── index.tsx
```

**步骤 3**: 实现组件
```typescript
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { QuizResult as QuizResultType } from "@/types/blocks/quiz-result";
import Icon from "@/components/icon";

export default function QuizResult({ result }: { result: QuizResultType }) {
  const percentage = (result.correctAnswers / result.totalQuestions) * 100;
  
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>测验结果</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center">
          <div className="text-4xl font-bold mb-4">
            {percentage.toFixed(0)}%
          </div>
          <p className="text-muted-foreground mb-4">
            你答对了 {result.correctAnswers} / {result.totalQuestions} 题
          </p>
          {result.feedback && (
            <p className="text-sm mb-6">{result.feedback}</p>
          )}
          <div className="flex gap-4 justify-center">
            <Button variant="outline">
              <Icon name="refresh" className="mr-2" />
              重新测试
            </Button>
            <Button>
              查看答案解析
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
```

### 3. 新增数据模型

如果组件需要数据库交互，创建对应的模型：

**步骤 1**: 创建模型文件
```bash
models/quiz-result.ts
```

**步骤 2**: 实现数据操作
```typescript
import { QuizResult } from "@/types/quiz-result";
import { getSupabaseClient } from "./db";

export async function saveQuizResult(result: QuizResult) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("quiz_results")
    .insert(result);
    
  if (error) throw error;
  return data;
}

export async function getQuizResults(userId: string) {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("quiz_results")
    .select("*")
    .eq("user_id", userId)
    .order("created_at", { ascending: false });
    
  if (error) throw error;
  return data;
}
```

## 最佳实践

### 1. 组件设计原则

- **单一职责**：每个组件只负责一个功能
- **可复用性**：设计时考虑在不同场景下的复用
- **类型安全**：所有 props 都要有明确的类型定义
- **响应式设计**：使用 Tailwind CSS 确保移动端适配

### 2. 命名规范

- **组件名**：使用 PascalCase，如 `QuizResult`
- **文件名**：使用 kebab-case，如 `quiz-result.tsx`
- **类型名**：与组件名保持一致，如 `QuizResultType`

### 3. 文件组织

- **简单组件**：直接在 `components/ui/` 下创建单个文件
- **复杂组件**：在 `components/blocks/` 下创建目录，包含：
  - `index.tsx` - 主组件
  - 子组件文件（如需要）
  - `styles.module.css` - 样式文件（如需要）

### 4. 样式处理

- 优先使用 Tailwind CSS 类
- 使用 `cn()` 工具函数合并类名
- 使用 `cva()` 创建组件变体

### 5. 状态管理

- 组件内部状态使用 React Hooks
- 跨组件状态使用 Context API
- 全局状态考虑使用状态管理库

### 6. 性能优化

- 使用 `React.memo` 优化纯组件
- 使用 `React.lazy` 实现代码分割
- 避免在渲染方法中创建新对象

## 常见问题

### Q: 什么时候创建新组件？
A: 当一段 UI 代码需要在多处复用，或者逻辑复杂度较高时，应该抽取为独立组件。

### Q: 组件应该放在哪个目录？
A: 
- 纯 UI 组件 → `components/ui/`
- 业务组件 → `components/blocks/`
- 页面特定组件 → 可以放在页面目录附近

### Q: 如何处理组件的国际化？
A: 使用项目的 i18n 系统，在组件中通过 `useTranslations` 钩子获取翻译文本。

### Q: 组件测试如何编写？
A: 在 `test/` 目录下创建对应的测试文件，使用 Jest 和 React Testing Library。

---

更新日期：2024-01-13
