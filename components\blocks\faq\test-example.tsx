/**
 * FAQ组件测试示例
 * 这个文件展示了如何使用新的FAQ Accordion组件的各种配置
 */

import FAQ from "./index";
import { Section } from "@/types/blocks/section";

// 示例FAQ数据
const sampleFAQData: Section = {
  name: "faq",
  label: "FAQ",
  title: "Frequently Asked Questions",
  description: "Find answers to common questions about our service.",
  items: [
    {
      title: "What is Nano Banana AI?",
      description: "Nano Banana AI is a revolutionary text-driven image editor that offers unprecedented character consistency and scene preservation capabilities."
    },
    {
      title: "How does character consistency work?",
      description: "Our advanced character consistency engine analyzes facial features, clothing, and distinctive characteristics to maintain subject appearance across multiple image generations."
    },
    {
      title: "Is there a free trial available?",
      description: "Yes! Our free Nano Banana trial offers 10 image generations per month with basic character consistency features at no cost."
    },
    {
      title: "What formats are supported?",
      description: "Nano Banana AI supports common image formats (PNG, JPG, WebP) with resolutions from 512x512 to 4K (4096x4096) for Pro users."
    }
  ]
};

// 测试组件示例
export function FAQTestExamples() {
  return (
    <div className="space-y-16 p-8">
      <h1 className="text-3xl font-bold text-center">FAQ Component Test Examples</h1>
      
      {/* 默认配置 - 向后兼容 */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">1. 默认配置（向后兼容）</h2>
        <p className="text-muted-foreground mb-6">
          使用默认设置：multiple模式，显示编号，无默认展开项
        </p>
        <FAQ section={sampleFAQData} />
      </div>

      {/* 单选模式 */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">2. 单选模式</h2>
        <p className="text-muted-foreground mb-6">
          一次只能展开一个FAQ项目，默认展开第一个
        </p>
        <FAQ 
          section={sampleFAQData} 
          accordionType="single"
          defaultOpen={0}
        />
      </div>

      {/* 多选模式，预设展开 */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">3. 多选模式，预设展开</h2>
        <p className="text-muted-foreground mb-6">
          可同时展开多个项目，默认展开第1和第3个
        </p>
        <FAQ 
          section={sampleFAQData} 
          accordionType="multiple"
          defaultOpen={[0, 2]}
        />
      </div>

      {/* 隐藏编号的简洁模式 */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">4. 简洁模式（无编号）</h2>
        <p className="text-muted-foreground mb-6">
          隐藏编号指示器，更简洁的视觉效果
        </p>
        <FAQ 
          section={sampleFAQData} 
          showNumbers={false}
        />
      </div>
    </div>
  );
}

export default FAQTestExamples;
