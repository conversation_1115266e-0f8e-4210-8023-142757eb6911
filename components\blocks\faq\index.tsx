import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Section as SectionType } from "@/types/blocks/section";

interface FAQProps {
  section: SectionType;
  /**
   * Accordion behavior type
   * - "single": Only one item can be open at a time
   * - "multiple": Multiple items can be open simultaneously
   */
  accordionType?: "single" | "multiple";
  /**
   * Default open items (by index)
   * For single type: number | undefined
   * For multiple type: number[] | undefined
   */
  defaultOpen?: number | number[];
  /**
   * Whether to show numbered indicators
   */
  showNumbers?: boolean;
}

export default function FAQ({
  section,
  accordionType = "multiple",
  defaultOpen,
  showNumbers = true
}: FAQProps) {
  if (section.disabled) {
    return null;
  }

  // Convert defaultOpen to the format expected by Radix UI
  const getDefaultValue = () => {
    if (!defaultOpen) return accordionType === "multiple" ? [] : undefined;

    if (accordionType === "single") {
      return typeof defaultOpen === "number" ? `item-${defaultOpen}` : undefined;
    } else {
      const openItems = Array.isArray(defaultOpen) ? defaultOpen : [defaultOpen];
      return openItems.map(index => `item-${index}`);
    }
  };

  return (
    <section id={section.name} className="py-16">
      <div className="container">
        <div className="text-center">
          {section.label && (
            <Badge className="text-xs font-medium">{section.label}</Badge>
          )}
          <h2 className="mt-4 text-4xl font-semibold">{section.title}</h2>
          <p className="mt-6 font-medium text-muted-foreground">
            {section.description}
          </p>
        </div>
        <div className="mx-auto mt-14 max-w-4xl">
          {accordionType === "single" ? (
            <Accordion
              type="single"
              className="w-full space-y-4"
              defaultValue={getDefaultValue() as string}
              collapsible
            >
              {section.items?.map((item, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="rounded-lg border border-border bg-card px-6 py-2 shadow-sm transition-all duration-200 hover:shadow-md focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
                >
                  <AccordionTrigger
                    className="text-left hover:no-underline [&[data-state=open]>div>span]:bg-primary [&[data-state=open]>div>span]:text-primary-foreground group"
                    aria-label={`Toggle FAQ: ${item.title}`}
                  >
                    <div className="flex items-center gap-4 w-full">
                      {showNumbers && (
                        <span className="flex size-8 shrink-0 items-center justify-center rounded-full border border-primary bg-background font-mono text-sm text-primary transition-colors group-hover:border-primary/80">
                          {index + 1}
                        </span>
                      )}
                      <h3 className="font-semibold text-foreground text-left flex-1 pr-4">
                        {item.title}
                      </h3>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="pt-2 pb-4">
                    <div className={`${showNumbers ? 'ml-12' : 'ml-0'} text-muted-foreground leading-relaxed`}>
                      {item.description}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          ) : (
            <Accordion
              type="multiple"
              className="w-full space-y-4"
              defaultValue={getDefaultValue() as string[]}
            >
              {section.items?.map((item, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="rounded-lg border border-border bg-card px-6 py-2 shadow-sm transition-all duration-200 hover:shadow-md focus-within:ring-2 focus-within:ring-primary focus-within:ring-offset-2"
                >
                  <AccordionTrigger
                    className="text-left hover:no-underline [&[data-state=open]>div>span]:bg-primary [&[data-state=open]>div>span]:text-primary-foreground group"
                    aria-label={`Toggle FAQ: ${item.title}`}
                  >
                    <div className="flex items-center gap-4 w-full">
                      {showNumbers && (
                        <span className="flex size-8 shrink-0 items-center justify-center rounded-full border border-primary bg-background font-mono text-sm text-primary transition-colors group-hover:border-primary/80">
                          {index + 1}
                        </span>
                      )}
                      <h3 className="font-semibold text-foreground text-left flex-1 pr-4">
                        {item.title}
                      </h3>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="pt-2 pb-4">
                    <div className={`${showNumbers ? 'ml-12' : 'ml-0'} text-muted-foreground leading-relaxed`}>
                      {item.description}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
        </div>
      </div>
    </section>
  );
}
